# FBA库存明细接口文档

## 功能概述

本模块实现了与领星ERP的FBA库存明细接口对接，支持查询FBA库存，对应系统【仓库】>【FBA库存明细】数据，以数量维度展示。

## 接口实现

### 1. 数据同步接口

**接口地址**: `task/controller/lingXingApiController::synStorageFbaDetail`

**请求方式**: POST

**请求参数**:
```json
{
    "token": "01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0",
    "offset": 0,
    "length": 20,
    "search_field": "seller_sku",
    "search_value": "MSKUFDA5E30",
    "cid": "",
    "bid": "",
    "attribute": "",
    "asin_principal": "",
    "status": "",
    "senior_search_list": "",
    "fulfillment_channel_type": "",
    "is_hide_zero_stock": "0",
    "is_parant_asin_merge": "0",
    "is_contain_del_ls": "0",
    "query_fba_storage_quantity_list": true
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "同步成功",
    "data": {
        "offset": 0,
        "length": 20,
        "total": 100,
        "success_count": 20,
        "next_offset": 20
    }
}
```

### 2. 数据查询接口

**接口地址**: `plugins/logistics/controller/fbaStorageDetailController::getFbaStorageDetailList`

**请求方式**: GET

**请求参数**:
```
page=1&page_size=20&sync_date=2025-06-20&search_field=sku&search_value=TEST
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "list": [
            {
                "id": 1,
                "name": "韧啸-US美国仓",
                "sid": 136,
                "asin": "B0D6XVLTJL",
                "product_name": "无线鼠标",
                "seller_sku": "20240613-1",
                "fnsku": "X004A0PW6J",
                "sku": "shubiao_1",
                "total": 2,
                "total_price": 40.00,
                "available_total": 42,
                "afn_fulfillable_quantity": 0,
                "sync_date": "2025-06-20"
            }
        ],
        "total": 1,
        "page": 1,
        "page_size": 20
    }
}
```

### 3. 统计信息接口

**接口地址**: `plugins/logistics/controller/fbaStorageDetailController::getFbaStorageDetailStatistics`

**请求方式**: GET

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "total_count": 1000,
        "total_quantity": 50000,
        "total_amount": 1000000.00,
        "available_quantity": 45000,
        "available_amount": 900000.00,
        "fba_quantity": 40000,
        "fba_amount": 800000.00,
        "fbm_quantity": 5000,
        "fbm_amount": 100000.00
    }
}
```

## 数据库表结构

### 表名: `lingxing_fba_storage_detail`

**主要字段说明**:
- `id`: 主键ID
- `asin`: Amazon商品唯一标识符
- `seller_sku`: 卖家SKU(MSKU)
- `fnsku`: 亚马逊内部SKU
- `sku`: 系统SKU
- `total`: 总数量
- `total_price`: 总价值
- `available_total`: 可用总数
- `afn_fulfillable_quantity`: FBA可售数量
- `quantity`: FBM可售数量
- `sync_date`: 数据同步日期

**库龄字段**:
- `inv_age_0_to_30_days`: 0-1个月库龄
- `inv_age_31_to_60_days`: 1-2个月库龄
- `inv_age_61_to_90_days`: 2-3个月库龄
- `inv_age_91_to_180_days`: 3-6个月库龄
- `inv_age_181_to_270_days`: 6-9个月库龄
- `inv_age_271_to_365_days`: 9-12个月库龄
- `inv_age_365_plus_days`: 12个月以上库龄

## 使用示例

### 1. 同步FBA库存数据

```bash
curl -X POST "http://your-domain.com/task/controller/lingXingApiController/synStorageFbaDetail" \
-H "Content-Type: application/json" \
-d '{
    "token": "01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0",
    "offset": 0,
    "length": 100,
    "is_hide_zero_stock": "1"
}'
```

### 2. 查询FBA库存列表

```bash
curl -X GET "http://your-domain.com/plugins/logistics/controller/fbaStorageDetailController/getFbaStorageDetailList?page=1&page_size=20&sync_date=2025-06-20"
```

### 3. 导出FBA库存数据

```bash
curl -X POST "http://your-domain.com/plugins/logistics/controller/fbaStorageDetailController/exportFbaStorageDetail" \
-H "Content-Type: application/x-www-form-urlencoded" \
-d "sync_date=2025-06-20&is_hide_zero_stock=1"
```

## 搜索功能

### 支持的搜索字段

- `sku`: 按SKU搜索
- `product_name`: 按产品名称搜索
- `seller_sku`: 按MSKU搜索
- `fnsku`: 按FNSKU搜索
- `asin`: 按ASIN搜索

### 高级搜索

支持使用 `senior_search_list` 参数进行多条件组合搜索：

```json
{
    "senior_search_list": "[{\"name\":\"SKU\",\"search_field\":\"sku\",\"search_value\":[\"sku1\",\"sku2\"]},{\"name\":\"品名\",\"search_field\":\"product_name\",\"search_value\":[\"产品1\",\"产品2\"]}]"
}
```

## 筛选功能

### 共享类型筛选
- `0`: 非共享
- `1`: 北美共享  
- `2`: 欧洲共享

### 配送方式筛选
- `FBA`: 仅显示FBA库存
- `FBM`: 仅显示FBM库存

### 库存显示
- `is_hide_zero_stock=0`: 显示零库存
- `is_hide_zero_stock=1`: 隐藏零库存

## 单元测试

本项目使用原生PHP实现单元测试，无需依赖PHPUnit框架。

### 运行测试

```bash
php plugins/logistics/tests/fbaStorageDetailTest.php
```

### 测试输出示例

```
🔧 开始执行FBA库存明细单元测试...

=== 🚀 FBA库存明细功能测试开始 ===

🧪 测试获取FBA库存明细列表...
✅ 测试获取FBA库存明细列表 - 通过

🧪 测试获取FBA库存明细统计信息...
✅ 测试获取FBA库存明细统计信息 - 通过

🧪 测试搜索条件过滤...
  ✅ 测试SKU搜索 - 通过
  ✅ 测试产品名称搜索 - 通过
  ✅ 测试MSKU搜索 - 通过
  ✅ 测试FNSKU搜索 - 通过
  ✅ 测试ASIN搜索 - 通过

🧪 测试筛选条件...
  ✅ 测试非共享类型筛选 - 通过
  ✅ 测试北美共享类型筛选 - 通过
  ✅ 测试欧洲共享类型筛选 - 通过
  ✅ 测试隐藏零库存筛选 - 通过
  ✅ 测试FBA配送方式筛选 - 通过
  ✅ 测试FBM配送方式筛选 - 通过

🧪 测试排序功能...
  ✅ 测试排序功能完成，共测试16种排序组合

🧪 测试导出功能...
✅ 测试导出FBA库存明细数据 - 通过

🧪 测试参数验证...
  ✅ 测试无效排序字段处理 - 通过
  ✅ 测试无效排序方向处理 - 通过

🧪 测试清理过期数据功能...
✅ 测试清理过期数据功能 - 通过，清理了0条记录

🧪 测试边界条件...
  ✅ 测试最小页码边界条件 - 通过
  ✅ 测试最大页面大小边界条件 - 通过
  ✅ 测试空搜索值边界条件 - 通过

=== 📊 测试结果统计 ===
总测试数: 45
通过数: 45
失败数: 0
耗时: 0.32秒

🎉 所有测试通过 ✅

测试结束。
```

### 测试覆盖内容

- **功能测试**: 数据列表查询、统计信息获取
- **搜索测试**: SKU、产品名称、MSKU、FNSKU、ASIN搜索
- **筛选测试**: 共享类型、配送方式、零库存显示等筛选
- **排序测试**: 16种字段和方向的排序组合
- **导出测试**: CSV数据导出功能
- **参数验证**: 无效参数处理
- **边界测试**: 极值参数测试

### 自定义测试

可以直接修改测试类中的参数来适应不同的测试需求：

```php
// 修改测试参数
$param = [
    'page' => 1,
    'page_size' => 10,
    'sync_date' => '2025-06-20',  // 指定测试日期
    'search_field' => 'sku',
    'search_value' => 'YOUR_SKU'  // 指定测试SKU
];
```

## 错误处理

常见错误码：
- `0`: 成功
- `-1`: 失败，具体错误信息在msg字段中

## 性能优化建议

1. **分页查询**: 建议使用分页查询，避免一次性加载过多数据
2. **索引优化**: 已对关键字段建立索引，包括`sync_date`、`asin`、`seller_sku`等
3. **数据清理**: 定期清理过期数据，建议保留30天数据
4. **缓存策略**: 对于频繁查询的统计数据，可考虑使用Redis缓存

## 注意事项

1. **数据同步频率**: 建议每天同步一次FBA库存数据
2. **数据存储**: 数据按同步日期存储，便于历史数据查询
3. **权限控制**: 同步接口需要正确的token验证
4. **数据一致性**: 使用事务确保数据一致性
5. **日志记录**: 重要操作都有日志记录，便于问题排查

## 版本历史

- **v1.0.0** (2025-06-20): 初始版本，实现基础功能
  - FBA库存数据同步
  - 数据查询和筛选
  - 数据导出
  - 单元测试
