# FBA发货单API接口测试工具 - 使用说明

## 📋 概述

这是一个全面优化的FBA发货单API接口测试工具，提供了完整的测试覆盖、性能监控、详细日志记录和报告生成功能。

## 🚀 主要功能

### ✨ 核心优化特性

- **🔧 配置外部化**: 支持环境变量配置，提升环境适配性
- **📊 性能监控**: 实时监控API响应时间和成功率
- **📝 详细日志**: 完整的请求/响应日志记录
- **📈 统计分析**: 自动生成性能和测试结果报告
- **🛠️ 增强错误处理**: 更详细的错误信息和异常处理
- **🧪 扩展测试用例**: 全面的参数验证和状态测试

### 🎯 测试覆盖范围

1. **参数验证测试**
   - Token验证功能
   - 必需参数检查
   - 参数格式验证

2. **发货单同步接口测试**
   - 基本API调用功能
   - 数据结构验证
   - 响应时间监控

3. **不同状态查询测试**
   - 待配货状态 (-1)
   - 待发货状态 (0)
   - 已发货状态 (1)
   - 已作废状态 (3)

## 🔧 配置说明

### 环境变量配置

创建 `.env` 文件（参考 `.env.example`）：

```bash
# API基础配置
API_BASE_URL=http://localhost/oa-api
API_TOKEN=your-api-token-here

# 超时和重试配置
API_TIMEOUT=30
API_RETRY_TIMES=3

# 调试和日志配置
DEBUG_MODE=false
LOG_REQUESTS=true

# 性能监控配置
PERFORMANCE_MODE=true
```

### 配置项说明

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `API_BASE_URL` | API服务器地址 | `http://localhost/oa-api` |
| `API_TOKEN` | API访问令牌 | 硬编码默认值 |
| `API_TIMEOUT` | 请求超时时间(秒) | `30` |
| `API_RETRY_TIMES` | 重试次数 | `3` |
| `DEBUG_MODE` | 调试模式 | `false` |
| `LOG_REQUESTS` | 是否记录请求日志 | `true` |
| `PERFORMANCE_MODE` | 是否启用性能监控 | `true` |

## 🚀 使用方法

### 基本使用

```bash
# 进入测试目录
cd plugins/logistics/tests

# 复制配置文件
cp .env.example .env

# 修改配置文件中的API地址和Token
nano .env

# 运行测试
php apiTest.php
```

### 高级用法

```bash
# 启用调试模式
DEBUG_MODE=true php apiTest.php

# 禁用日志记录
LOG_REQUESTS=false php apiTest.php

# 使用自定义API地址
API_BASE_URL=https://your-api-server.com/api php apiTest.php
```

## 📊 输出说明

### 控制台输出

测试运行时会显示：

```
=== FBA发货单API接口测试开始 ===
📅 开始时间: 2025-06-23 14:22:19
🔧 配置信息: http://localhost/oa-api
📝 日志文件: /path/to/logs/api_test_2025-06-23.log

--- 参数验证测试 ---
测试参数验证...
✓ Token验证功能正常 (响应时间: 45.32ms)
✅ 测试完成，耗时: 50.15ms

--- 发货单同步接口测试 ---
测试发货单同步接口...
✓ API调用成功: 获取数据成功 (响应时间: 123.45ms)
  ✓ 数据结构验证通过
✅ 测试完成，耗时: 128.67ms

--- 不同状态查询测试 ---
测试不同状态查询...
  ✓ 状态 -1 (待配货) 查询成功 (响应时间: 98.21ms)
  ✓ 状态 0 (待发货) 查询成功 (响应时间: 87.65ms)
  ✓ 状态 1 (已发货) 查询成功 (响应时间: 92.34ms)
  ✓ 状态 3 (已作废) 查询成功 (响应时间: 89.12ms)
  📊 状态查询测试统计: 4/4 通过
✅ 测试完成，耗时: 2456.78ms

=== API测试完成 ===
⏱️ 总耗时: 2635.60ms
📊 总测试数: 3
✅ 通过测试: 3
❌ 失败测试: 0
📈 通过率: 100.00%

🚀 === 性能统计报告 ===
📈 总请求数: 7
✅ 成功请求: 7 (100.00%)
⚡ 平均响应时间: 93.44ms
🏃 最快响应: 45.32ms
🐌 最慢响应: 123.45ms

📋 === 测试结果详情 ===
🔸 参数验证测试: ✅ 通过
   📝 说明: Token验证功能正常
🔸 发货单同步接口测试: ✅ 通过
   📝 说明: API调用成功，数据结构正确
🔸 不同状态查询测试: ✅ 通过
   📝 说明: 状态查询测试完成: 4/4 个状态测试通过

📄 详细报告已保存: /path/to/reports/api_test_report_2025-06-23_14-22-19.json

🎉 所有API测试通过！
```

### 生成的文件

#### 1. 日志文件
- 位置: `plugins/logistics/logs/api_test_YYYY-MM-DD.log`
- 内容: 详细的请求/响应日志和错误信息

#### 2. 测试报告
- 位置: `plugins/logistics/reports/api_test_report_YYYY-MM-DD_HH-mm-ss.json`
- 内容: 完整的测试结果和性能数据

## 📁 目录结构

```
plugins/logistics/tests/
├── apiTest.php              # 主测试文件
├── .env.example            # 配置示例文件
├── README_API_TEST.md      # 使用说明文档
├── logs/                   # 日志目录（自动创建）
│   └── api_test_*.log      # 日志文件
└── reports/                # 报告目录（自动创建）
    └── api_test_report_*.json  # 测试报告
```

## 🛠️ 故障排除

### 常见问题

1. **配置错误**
   ```
   错误: API基础配置不完整：需要base_url和token
   解决: 检查.env文件中的API_BASE_URL和API_TOKEN配置
   ```

2. **网络连接问题**
   ```
   错误: CURL错误: Connection refused
   解决: 确认API服务器地址正确且服务正常运行
   ```

3. **权限问题**
   ```
   错误: ✗ Token验证功能异常
   解决: 检查API_TOKEN是否正确且有效
   ```

### 调试技巧

1. **启用调试模式**
   ```bash
   DEBUG_MODE=true php apiTest.php
   ```

2. **查看详细日志**
   ```bash
   tail -f plugins/logistics/logs/api_test_$(date +%Y-%m-%d).log
   ```

3. **检查网络连通性**
   ```bash
   curl -X POST "http://localhost/oa-api/task/controller/lingXingApiController.php" \
        -d "token=your-token&action=synInboundShipment"
   ```

## 🔄 版本更新

### v2.0 (当前版本)
- ✅ 配置外部化支持
- ✅ 性能监控和统计
- ✅ 详细日志记录
- ✅ 测试报告生成
- ✅ 增强错误处理
- ✅ 扩展测试用例

### v1.0 (原始版本)
- ✅ 基本API测试功能
- ✅ 简单结果输出

## 📞 技术支持

如果在使用过程中遇到问题，请：

1. 检查配置文件是否正确
2. 查看日志文件中的错误信息
3. 确认API服务器状态
4. 验证网络连接

## 🎯 扩展建议

可以根据需要进一步扩展的功能：

1. **并发测试**: 添加多线程并发请求测试
2. **压力测试**: 增加高负载场景测试
3. **数据验证**: 加强返回数据的业务逻辑验证
4. **自动化CI**: 集成到持续集成流程中
5. **邮件通知**: 测试失败时发送邮件通知
6. **Web界面**: 开发Web界面进行测试管理
