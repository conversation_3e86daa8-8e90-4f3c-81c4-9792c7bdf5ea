# 记忆恢复指令 - 海外仓备货单SKU拆分项目

## 立即执行的唤醒步骤

### 第一步：读取项目记忆文件
```
使用view工具读取：.augment/tasks/overseas_inbound_sku_split.xml
这个文件包含完整的项目信息、技术架构、实施计划和上下文恢复信息
```

### 第二步：理解项目核心
**项目目标**：将海外仓备货单数据从备货单维度拆分为【备货单号+SKU】维度的明细数据

**关键信息**：
- 源数据：dbErpMysql.lingxing_overseas_inbound表的products JSON字段
- 目标：创建包含19个字段的明细表
- 数据示例：products字段包含SKU数组，每个SKU有完整信息

### 第三步：检查当前任务状态
```
查看.augment/tasks/overseas_inbound_sku_split.xml中的implementation_plan部分
确认哪些任务已完成（status="completed"），哪些正在进行（status="in_progress"）
```

### 第四步：读取关键文件
按优先级读取以下文件来恢复上下文：
1. `plugins/logistics/models/overseasInboundModel.php` - 现有模型
2. `plugins/logistics/sql/overseas_inbound.sql` - 原始表结构
3. `task/controller/lingXingApiController.php` - API控制器
4. `task/shell/lingxing_overseas_inbound.sh` - Shell脚本

## 任务执行指导

### 开发阶段任务优先级
1. **高优先级**：数据结构分析、目标表设计、拆分逻辑实现
2. **中优先级**：控制器方法、导入导出功能
3. **低优先级**：测试、部署

### 代码实现模式
- 使用现有的数据库连接类：dbErpMysql、dbLMysql
- 遵循现有的模型结构模式
- 分批处理：500条每批
- 事务安全：使用beginTransaction/commit/rollBack

### 文件创建规范
- 模型文件：`plugins/logistics/models/overseasInboundDetailModel.php`
- SQL文件：`plugins/logistics/sql/overseas_inbound_detail.sql`
- 测试文件：`plugins/logistics/tests/overseasInboundDetailTest.php`

## 任务完成后的更新流程

### 更新任务状态
```xml
将完成的任务状态从status="pending"改为status="completed"
添加完成时间和相关文件信息
```

### 记录实现细节
在xml文件中添加：
- 实际创建的文件路径
- 关键方法名称
- 重要的技术决策
- 遇到的问题和解决方案

### 更新下一步行动
- 移除已完成的immediate actions
- 添加新发现的任务或问题
- 更新优先级

## 上下文容量管理

### 容量预警信号
- 响应变慢
- 开始重复之前的内容
- 无法记住最近的对话

### 容量满时的处理
1. 立即更新.augment/tasks/overseas_inbound_sku_split.xml
2. 记录当前进度和下一步计划
3. 保存所有重要的技术决策
4. 通知用户准备重启对话

## 项目特定的技术要点

### products JSON结构
```json
[{
  "sku": "TROI-BLACK",
  "product_name": "特洛伊-黑色", 
  "fnsku": "",
  "stock_num": 150,
  "batch_record_list": [{"good_num": 150}]
}]
```

### 字段映射规则
- sku → sku
- product_name → product_name
- fnsku → fnsku
- stock_num → quantity
- batch_record_list[0].good_num → box_count

### 导入导出功能要求
- 新增导入：创建新的明细记录
- 批量修改导入：更新现有记录
- 导出：支持Excel格式

## 错误恢复指导

### 如果记忆文件损坏
1. 重新分析现有代码结构
2. 查看git历史或备份
3. 重新创建基础的项目记忆文件

### 如果忘记技术细节
1. 查看现有模型文件的实现模式
2. 参考类似功能的代码
3. 重新分析数据库表结构

### 如果不确定进度
1. 检查文件系统中已存在的文件
2. 查看数据库中是否有新表
3. 询问用户当前状态

## 与用户沟通指导

### 进度汇报格式
- 明确说明当前完成的任务
- 列出下一步计划
- 询问是否有新的需求或变更

### 技术决策确认
- 重要的架构决策需要用户确认
- 数据库表结构设计需要审核
- 业务逻辑实现需要验证

### 问题求助
- 遇到技术难题时及时求助
- 不确定业务逻辑时主动询问
- 需要额外信息时明确提出

## 项目成功标准

### 功能完整性
- SKU拆分逻辑正确实现
- 导入导出功能正常工作
- 集成到现有流程

### 数据一致性
- 拆分前后数据量一致
- 字段映射准确无误
- 异常数据正确处理

### 性能要求
- 大数据量处理不超时
- 内存使用控制在合理范围
- 批量操作效率高

记住：这是一个重要的数据处理项目，需要确保数据准确性和系统稳定性。每一步都要仔细验证，遇到不确定的地方要及时与用户沟通确认。
