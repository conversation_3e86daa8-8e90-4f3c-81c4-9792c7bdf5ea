# 记忆恢复指令 - 物流模块项目管理系统

## 立即执行的唤醒步骤

### 第一步：读取任务注册表
```
使用view工具读取：.augment/tasks/task_registry.xml
这个文件包含所有项目的注册信息和全局上下文
```

### 第二步：确定当前工作项目
根据用户指令确定要处理的项目：

**如果是已完成项目的维护**：
- 读取对应的项目文件（如：overseas_inbound_sku_split.xml）
- 了解项目现状和技术架构

**如果是新项目开发**：
- 使用next_project_template创建新项目文件
- 根据需求描述填充项目信息

### 第三步：读取项目具体信息
```
根据项目ID读取具体项目文件：
.augment/tasks/[project_id].xml
```

### 第四步：读取相关技术文件
根据项目需要读取相关文件来恢复技术上下文：
- 现有模型文件：`plugins/logistics/models/`
- 控制器文件：`task/controller/logisticsController.php`
- 数据库文件：`plugins/logistics/sql/`
- 测试文件：`plugins/logistics/tests/`

## 项目类型识别

### 新项目开发
**识别标志**：用户提供新的需求描述
**处理流程**：
1. 创建新的项目XML文件
2. 分析需求并设计技术方案
3. 制定实施计划
4. 更新任务注册表

### 现有项目维护
**识别标志**：用户提到已有功能的修改或扩展
**处理流程**：
1. 读取现有项目文件
2. 了解当前实现状态
3. 分析变更需求
4. 制定增量开发计划

## 任务执行指导

### 开发阶段任务优先级
1. **高优先级**：需求分析、数据库设计、核心逻辑实现
2. **中优先级**：控制器方法、业务功能实现
3. **低优先级**：测试、文档、部署

### 代码实现模式
- 使用现有的数据库连接类：dbErpMysql、dbLMysql
- 遵循现有的模型结构模式
- 分批处理：500条每批
- 事务安全：使用beginTransaction/commit/rollBack

### 文件创建规范
- 模型文件：`plugins/logistics/models/overseasInboundDetailModel.php`
- SQL文件：`plugins/logistics/sql/overseas_inbound_detail.sql`
- 测试文件：`plugins/logistics/tests/overseasInboundDetailTest.php`

## 任务完成后的更新流程

### 更新任务状态
```xml
将完成的任务状态从status="pending"改为status="completed"
添加完成时间和相关文件信息
```

### 记录实现细节
在xml文件中添加：
- 实际创建的文件路径
- 关键方法名称
- 重要的技术决策
- 遇到的问题和解决方案

### 更新下一步行动
- 移除已完成的immediate actions
- 添加新发现的任务或问题
- 更新优先级

## 上下文容量管理

### 容量预警信号
- 响应变慢
- 开始重复之前的内容
- 无法记住最近的对话

### 容量满时的处理
1. 立即更新.augment/tasks/overseas_inbound_sku_split.xml
2. 记录当前进度和下一步计划
3. 保存所有重要的技术决策
4. 通知用户准备重启对话

## 新项目创建流程

### 当用户提供新需求时
1. **需求分析**：仔细分析用户的需求描述
2. **项目识别**：确定这是一个新项目还是现有项目的扩展
3. **创建项目文件**：
   ```
   根据需求创建新的项目XML文件
   使用.augment/tasks/project_creator.php工具辅助创建
   ```
4. **更新注册表**：将新项目添加到task_registry.xml的active_projects中

### 项目文件命名规范
- 文件名：`[project_id].xml`
- 项目ID：英文小写，下划线分隔
- 示例：`inventory_management.xml`、`shipping_optimization.xml`

### 需求分析要点
- **业务目标**：用户想要解决什么问题
- **技术需求**：需要什么技术实现
- **数据需求**：涉及哪些数据表和字段
- **集成需求**：与现有系统如何集成

## 错误恢复指导

### 如果记忆文件损坏
1. 重新分析现有代码结构
2. 查看git历史或备份
3. 重新创建基础的项目记忆文件

### 如果忘记技术细节
1. 查看现有模型文件的实现模式
2. 参考类似功能的代码
3. 重新分析数据库表结构

### 如果不确定进度
1. 检查文件系统中已存在的文件
2. 查看数据库中是否有新表
3. 询问用户当前状态

## 与用户沟通指导

### 进度汇报格式
- 明确说明当前完成的任务
- 列出下一步计划
- 询问是否有新的需求或变更

### 技术决策确认
- 重要的架构决策需要用户确认
- 数据库表结构设计需要审核
- 业务逻辑实现需要验证

### 问题求助
- 遇到技术难题时及时求助
- 不确定业务逻辑时主动询问
- 需要额外信息时明确提出

## 项目成功标准

### 功能完整性
- SKU拆分逻辑正确实现
- 导入导出功能正常工作
- 集成到现有流程

### 数据一致性
- 拆分前后数据量一致
- 字段映射准确无误
- 异常数据正确处理

### 性能要求
- 大数据量处理不超时
- 内存使用控制在合理范围
- 批量操作效率高

## 多项目管理要点

### 项目状态管理
- **planning**: 需求分析和设计阶段
- **development**: 开发实现阶段
- **testing**: 测试验证阶段
- **completed**: 已完成部署
- **maintenance**: 维护阶段

### 任务优先级管理
- **high**: 核心功能，阻塞性任务
- **medium**: 重要功能，影响用户体验
- **low**: 优化功能，可延后处理

### 项目间依赖管理
- 检查新项目是否依赖已有项目的功能
- 确保不会影响已完成项目的稳定性
- 合理复用已有的代码和架构

### 版本控制要点
- 每个项目独立管理
- 重要变更记录在项目文件中
- 保持向后兼容性

## 任务完成通知系统

### 系统通知功能
每次完成任务后，必须执行系统通知：
```powershell
D:\phpstudy_pro\WWW\notify.ps1 -title "秋儿已经为您实现了$fun功能" -message "为您节省了$time时间"
```

### 通知参数说明
- **$fun**: 替换为具体实现的功能名称
- **$time**: 替换为预估节省的时间（如：2小时、1天、3天等）

### 通知时机
- ✅ 单个重要功能完成时
- ✅ 整个项目阶段完成时
- ✅ 所有项目任务完成时
- ✅ 重要bug修复完成时

### 通知示例
```powershell
# 功能开发完成
D:\phpstudy_pro\WWW\notify.ps1 -title "秋儿已经为您实现了数据导入导出功能" -message "为您节省了2天时间"

# 项目完成
D:\phpstudy_pro\WWW\notify.ps1 -title "秋儿已经为您实现了海外仓库存管理系统" -message "为您节省了1周时间"

# 优化完成
D:\phpstudy_pro\WWW\notify.ps1 -title "秋儿已经为您优化了数据处理性能" -message "为您节省了调试时间"
```

### 执行要求
- 必须在任务完成的最后步骤执行
- 使用launch-process工具执行
- 设置合理的等待时间（10秒）
- 确保通知内容准确描述完成的工作

记住：这是一个多项目管理系统，需要确保各项目间的协调性和系统整体稳定性。每个新项目都要仔细分析对现有系统的影响。完成任务后务必发送系统通知。
