<?php
/**
 * 海外仓备货单模型
 * @purpose 海外仓备货单数据处理
 * @Author: System
 * @Time: 2025/06/24
 */

namespace plugins\logistics\models;

use core\lib\db\dbLMysql;

class overseasInboundModel
{
    private $db;
    
    public function __construct()
    {
        $this->db = dbLMysql::getInstance();
    }

    /**
     * 获取备货单列表
     * @param array $params 查询参数
     * @return array
     */
    public function getInboundList($params = [])
    {
        $where = 'status in (50, 60)';
        $whereData = [];
        
        // 状态筛选
        if (isset($params['status']) && $params['status'] !== '') {
            $where .= ' AND status = :status';
            $whereData['status'] = $params['status'];
        }
        
        // 子状态筛选
        if (isset($params['sub_status']) && $params['sub_status'] !== '') {
            $where .= ' AND sub_status = :sub_status';
            $whereData['sub_status'] = $params['sub_status'];
        }
        
        // 发货仓库筛选
        if (!empty($params['s_wid'])) {
            if (is_array($params['s_wid'])) {
                $this->db->whereIn('s_wid', $params['s_wid']);
            } else {
                $where .= ' AND s_wid = :s_wid';
                $whereData['s_wid'] = $params['s_wid'];
            }
        }
        
        // 收货仓库筛选
        if (!empty($params['r_wid'])) {
            if (is_array($params['r_wid'])) {
                $this->db->whereIn('r_wid', $params['r_wid']);
            } else {
                $where .= ' AND r_wid = :r_wid';
                $whereData['r_wid'] = $params['r_wid'];
            }
        }
        
        // 备货单号筛选
        if (!empty($params['overseas_order_no'])) {
            $where .= ' AND overseas_order_no LIKE :overseas_order_no';
            $whereData['overseas_order_no'] = '%' . $params['overseas_order_no'] . '%';
        }
        
        // 时间筛选
        if (!empty($params['create_time_from'])) {
            $dateType = $params['date_type'] ?? 'create_time';
            $where .= " AND {$dateType} >= :create_time_from";
            $whereData['create_time_from'] = $params['create_time_from'];
        }
        
        if (!empty($params['create_time_to'])) {
            $dateType = $params['date_type'] ?? 'create_time';
            $where .= " AND {$dateType} <= :create_time_to";
            $whereData['create_time_to'] = $params['create_time_to'] . ' 23:59:59';
        }
        
        // 删除状态筛选
        if (isset($params['is_delete'])) {
            if ($params['is_delete'] == 2) {
                // 全部
                $where = str_replace('is_deleted = 0', '1=1', $where);
            } elseif ($params['is_delete'] == 1) {
                // 已删除
                $where = str_replace('is_deleted = 0', 'is_deleted = 1', $where);
            }
            // 默认未删除，不需要修改
        }
        
        $page = $params['page'] ?? 1;
        $pageSize = $params['page_size'] ?? 20;
        $pageSize = min($pageSize, 50); // 最大50条
        
        return $this->db->table('overseas_inbound')
            ->field('*')
            ->where($where, $whereData)
            ->order('id DESC')
            ->pages($page, $pageSize);
    }

    /**
     * 获取单条备货单详情
     * @param int $id
     * @return array|false
     */
    public function getInboundDetail($id)
    {
        $data = $this->db->table('overseas_inbound')
            ->where('id = :id', ['id' => $id])
            ->one();
            
        if ($data) {
            // 解析JSON字段
            $data['products'] = json_decode($data['products'] ?? '[]', true);
            $data['logistics'] = json_decode($data['logistics'] ?? '[]', true);
            $data['head_logistics_list'] = json_decode($data['head_logistics_list'] ?? '{}', true);
        }
        
        return $data;
    }

    /**
     * 根据备货单号获取详情
     * @param string $overseasOrderNo
     * @return array|false
     */
    public function getInboundByOrderNo($overseasOrderNo)
    {
        $data = $this->db->table('overseas_inbound')
            ->where('overseas_order_no = :overseas_order_no', ['overseas_order_no' => $overseasOrderNo])
            ->one();
            
        if ($data) {
            // 解析JSON字段
            $data['products'] = json_decode($data['products'] ?? '[]', true);
            $data['logistics'] = json_decode($data['logistics'] ?? '[]', true);
            $data['head_logistics_list'] = json_decode($data['head_logistics_list'] ?? '{}', true);
        }
        
        return $data;
    }

    /**
     * 保存备货单数据
     * @param array $data
     * @return bool
     */
    public function saveInboundData($data)
    {
        try {
            $this->db->beginTransaction();
            
            $successCount = 0;
            foreach ($data as $item) {
                if ($this->saveInboundItem($item)) {
                    $successCount++;
                }
            }
            
            $this->db->commit();
            return $successCount;
        } catch (\Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /**
     * 保存单个备货单项
     * @param array $item
     * @return bool
     */
    private function saveInboundItem($item)
    {
        // 检查是否已存在
        $existing = $this->db->table('overseas_inbound')
            ->where('overseas_order_no = :overseas_order_no', ['overseas_order_no' => $item['overseas_order_no']])
            ->one();

        // 准备数据
        $saveData = [
            'overseas_order_no' => $item['overseas_order_no'] ?? '',
            'inbound_order_no' => $item['inbound_order_no'] ?? '',
            'customer_reference_no' => $item['customer_reference_no'] ?? '',
            's_wid' => $item['s_wid'] ?? 0,
            's_wname' => $item['s_wname'] ?? '',
            'r_wid' => $item['r_wid'] ?? 0,
            'r_wname' => $item['r_wname'] ?? '',
            'logistics_id' => $item['logistics_id'] ?? 0,
            'logistics_name' => $item['logistics_name'] ?? '',
            'remark' => $item['remark'] ?? '',
            'status' => $item['status'] ?? 0,
            'rollback_remark' => $item['rollback_remark'] ?? '',
            'is_delete' => $item['is_delete'] ?? 0,
            'uid' => $item['uid'] ?? 0,
            'create_user' => $item['create_user'] ?? '',
            'update_user' => $item['update_user'] ?? '',
            'create_time' => $item['create_time'] ?? date('Y-m-d H:i:s'),
            'estimated_time' => $item['estimated_time'] ?: null,
            'audit_handle_time' => $item['audit_handle_time'] ?: null,
            'send_good_handle_time' => $item['send_good_handle_time'] ?: null,
            'receive_good_handle_time' => $item['receive_good_handle_time'] ?: null,
            'real_delivery_time' => $item['real_delivery_time'] ?: null,
            'update_time' => $item['update_time'] ?: date('Y-m-d H:i:s'),
            'products' => json_encode($item['products'] ?? [], JSON_UNESCAPED_UNICODE),
            'logistics' => json_encode($item['logistics'] ?? [], JSON_UNESCAPED_UNICODE),
            'logistics_list_type' => $item['logistics_list_type'] ?? 0,
            'head_logistics_list' => json_encode($item['head_logistics_list'] ?? [], JSON_UNESCAPED_UNICODE),
            'sync_date' => date('Y-m-d'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // 设置默认可编辑字段
        if (!$existing) {
            $saveData['warehouse_code'] = $item['overseas_order_no']; // 默认为备货单号
            $saveData['created_at'] = date('Y-m-d H:i:s');
        }

        if ($existing) {
            // 更新现有记录，保留可编辑字段
            unset($saveData['transparent_label']);
            unset($saveData['warehouse_code']);
            unset($saveData['shop_code']);
            unset($saveData['fnsku']);
            unset($saveData['remaining_available']);
            unset($saveData['shipping_remark']);
            unset($saveData['other_remark']);
            unset($saveData['created_at']);
            
            return $this->db->table('overseas_inbound')
                ->where('id = :id', ['id' => $existing['id']])
                ->update($saveData);
        } else {
            // 插入新记录
            return $this->db->table('overseas_inbound')->insert($saveData);
        }
    }

    /**
     * 更新可编辑字段
     * @param int $id
     * @param array $data
     * @return int
     */
    public function updateEditableFields($id, $data)
    {
        $allowedFields = [
            'transparent_label',
            'warehouse_code', 
            'shop_code',
            'fnsku',
            'remaining_available',
            'shipping_remark',
            'other_remark'
        ];
        
        $updateData = [];
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updateData[$field] = $data[$field];
            }
        }
        
        if (empty($updateData)) {
            return 0;
        }
        
        $updateData['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->table('overseas_inbound')
            ->where('id = :id', ['id' => $id])
            ->update($updateData);
    }

    /**
     * 删除备货单（逻辑删除）
     * @param int $id
     * @return int
     */
    public function deleteInbound($id)
    {
        return $this->db->table('overseas_inbound')
            ->where('id = :id', ['id' => $id])
            ->update([
                'is_deleted' => 1,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
    }

    /**
     * 获取状态映射
     * @return array
     */
    public function getStatusMap()
    {
        return [
            10 => '待审核',
            20 => '已驳回',
            30 => '待配货',
            40 => '待发货',
            50 => '待收货',
            51 => '已撤销',
            60 => '已完成'
        ];
    }

    /**
     * 获取子状态映射
     * @return array
     */
    public function getSubStatusMap()
    {
        return [
            0 => '全部',
            1 => '未收货',
            2 => '部分收货'
        ];
    }

    /**
     * 获取时间类型映射
     * @return array
     */
    public function getDateTypeMap()
    {
        return [
            'delivery_time' => '发货时间',
            'create_time' => '创建时间',
            'receive_time' => '收货时间',
            'update_time' => '更新时间'
        ];
    }

    /**
     * 验证备货单参数
     * @param array $params
     * @return array
     */
    public function validateParams($params)
    {
        $errors = [];
        
        // 验证状态
        if (isset($params['status']) && $params['status'] !== '') {
            $validStatus = [10, 20, 30, 40, 50, 51, 60];
            if (!in_array($params['status'], $validStatus)) {
                $errors[] = '无效的状态值';
            }
        }
        
        // 验证子状态
        if (isset($params['sub_status']) && $params['sub_status'] !== '') {
            $validSubStatus = [0, 1, 2];
            if (!in_array($params['sub_status'], $validSubStatus)) {
                $errors[] = '无效的子状态值';
            }
        }
        
        // 验证分页参数
        if (isset($params['page_size']) && $params['page_size'] > 50) {
            $errors[] = '分页数量不能超过50';
        }
        
        // 验证时间格式
        if (!empty($params['create_time_from'])) {
            if (!$this->validateDate($params['create_time_from'])) {
                $errors[] = '开始时间格式错误';
            }
        }
        
        if (!empty($params['create_time_to'])) {
            if (!$this->validateDate($params['create_time_to'])) {
                $errors[] = '结束时间格式错误';
            }
        }
        
        // 验证时间类型
        if (!empty($params['date_type'])) {
            $validDateTypes = ['delivery_time', 'create_time', 'receive_time', 'update_time'];
            if (!in_array($params['date_type'], $validDateTypes)) {
                $errors[] = '无效的时间类型';
            }
        }
        
        return $errors;
    }

    /**
     * 验证日期格式
     * @param string $date
     * @return bool
     */
    private function validateDate($date)
    {
        // 支持 Y-m-d 和 Y-m-d H:i:s 格式
        $patterns = [
            '/^\d{4}-\d{2}-\d{2}$/',
            '/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $date)) {
                return true;
            }
        }
        
        return false;
    }
}
