<?php
/**
 * FBA库存汇总统计执行脚本
 * @purpose 执行FBA库存汇总统计任务的示例脚本
 * @Author: System
 * @Time: 2025/06/20
 */

define('SELF_FK', realpath('')); //当前框架所在的根目录
//define('SELF_FK', $_SERVER['DOCUMENT_ROOT']); //当前框架所在的根目录
define('CORE', SELF_FK.'/core'); //核心文件目录
define('DEBUG', true); //调试模式设置
define('LOG_PATH', SELF_FK.'/log'); //调试模式设置
define('SYS_PUBLISH',false); //系统发版中

define('ROOT', SELF_FK."/");
require_once "environment.php"; //系统环境
require_once ROOT."/vendor/autoload.php";
include  CORE.'/common/function.php';
include  CORE.'/common/return.php';
include  CORE.'/common/phperror.php';
//方法请求
include CORE.'/fk.php';
//自动加载类
spl_autoload_register('\core\fk::load');

use plugins\logistics\models\fbaStorageSummaryModel;

/**
 * 执行FBA库存汇总统计
 */
function executeFbaStorageSummary($syncDate = null)
{
    if (empty($syncDate)) {
        $syncDate = date('Y-m-d');
    }
    
    echo "====================================\n";
    echo "FBA库存汇总统计执行脚本\n";
    echo "====================================\n";
    echo "执行日期: {$syncDate}\n";
    echo "开始时间: " . date('Y-m-d H:i:s') . "\n";
    echo "====================================\n\n";
    
    try {
        $model = new fbaStorageSummaryModel();
        
        echo "🔄 开始执行汇总统计...\n";
        
        // 执行完整汇总流程
        $startTime = microtime(true);
        $result = $model->executeFullSummary($syncDate);
        $endTime = microtime(true);
        
        if ($result) {
            echo "✅ 汇总统计执行成功！\n";
            echo "⏱️  执行耗时: " . round($endTime - $startTime, 2) . " 秒\n\n";
            
            // 输出统计结果
            showSummaryStatistics($model, $syncDate);
            
        } else {
            echo "❌ 汇总统计执行失败！\n";
            return false;
        }
        
    } catch (\Exception $e) {
        echo "❌ 执行过程中发生错误: " . $e->getMessage() . "\n";
        echo "📍 错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
        return false;
    }
    
    echo "====================================\n";
    echo "执行完成时间: " . date('Y-m-d H:i:s') . "\n";
    echo "====================================\n";
    
    return true;
}

/**
 * 显示汇总统计结果
 */
function showSummaryStatistics($model, $syncDate)
{
    echo "📊 汇总统计结果:\n";
    echo "------------------------------------\n";
    
    $levelTypeMap = $model->getLevelTypeMap();
    
    foreach ([1, 2, 3, 4] as $levelType) {
        $params = [
            'level_type' => $levelType,
            'sync_date' => $syncDate,
            'page' => 1,
            'page_size' => 1
        ];
        
        $result = $model->getSummaryList($params);
        $levelName = $levelTypeMap[$levelType];
        $count = $result['total'];
        
        echo sprintf("%-10s: %6d 条记录\n", $levelName, $count);
    }
    
    echo "------------------------------------\n\n";
}

/**
 * 验证数据完整性
 */
function validateDataIntegrity($model, $syncDate)
{
    echo "🔍 验证数据完整性...\n";
    
    try {
        // 检查各层级数据数量是否合理
        $shopLevel = $model->getSummaryList(['level_type' => 1, 'sync_date' => $syncDate, 'page_size' => 1])['total'];
        $skuLevel = $model->getSummaryList(['level_type' => 3, 'sync_date' => $syncDate, 'page_size' => 1])['total'];
        $siteLevel = $model->getSummaryList(['level_type' => 2, 'sync_date' => $syncDate, 'page_size' => 1])['total'];
        $asinLevel = $model->getSummaryList(['level_type' => 4, 'sync_date' => $syncDate, 'page_size' => 1])['total'];
        
        echo "数据层级验证:\n";
        echo "- 店铺级记录: {$shopLevel}\n";
        echo "- SKU级记录:  {$skuLevel}\n"; 
        echo "- 站点级记录: {$siteLevel}\n";
        echo "- ASIN级记录: {$asinLevel}\n";
        
        // 基本逻辑验证
        if ($skuLevel > $shopLevel) {
            echo "⚠️  警告: SKU级记录数大于店铺级记录数，数据可能异常\n";
        }
        
        if ($asinLevel > $siteLevel) {
            echo "⚠️  警告: ASIN级记录数大于站点级记录数，数据可能异常\n";
        }
        
        if ($shopLevel > 0 && $asinLevel == 0) {
            echo "❌ 错误: 存在店铺级数据但ASIN级数据为空\n";
            return false;
        }
        
        echo "✅ 数据完整性验证通过\n\n";
        return true;
        
    } catch (\Exception $e) {
        echo "❌ 数据完整性验证失败: " . $e->getMessage() . "\n";
        return false;
    }
}

/**
 * 清理历史数据
 */
function cleanHistoryData($model, $keepDays = 30)
{
    echo "🧹 清理历史数据 (保留 {$keepDays} 天)...\n";
    
    try {
        $cutoffDate = date('Y-m-d', strtotime("-{$keepDays} days"));
        
        // 这里应该实现清理逻辑，但为了安全暂时只输出
        echo "📅 将清理 {$cutoffDate} 之前的数据\n";
        echo "ℹ️  实际清理功能需要根据业务需求实现\n\n";
        
        return true;
        
    } catch (\Exception $e) {
        echo "❌ 清理历史数据失败: " . $e->getMessage() . "\n";
        return false;
    }
}

/**
 * 主执行函数
 */
function main($argv)
{
    // 解析命令行参数
    $syncDate = isset($argv[1]) ? $argv[1] : date('Y-m-d');
    $validate = isset($argv[2]) && $argv[2] === '--validate';
    $clean = isset($argv[3]) && $argv[3] === '--clean';
    
    // 验证日期格式
    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $syncDate)) {
        echo "❌ 错误: 日期格式无效，请使用 Y-m-d 格式\n";
        echo "用法: php executeFbaSummary.php [日期] [--validate] [--clean]\n";
        exit(1);
    }
    
    // 执行汇总统计
    $success = executeFbaStorageSummary($syncDate);
    
    if (!$success) {
        exit(1);
    }
    
    // 可选的数据验证
    if ($validate) {
        $model = new fbaStorageSummaryModel();
        $validateSuccess = validateDataIntegrity($model, $syncDate);
        
        if (!$validateSuccess) {
            exit(1);
        }
    }
    
    // 可选的历史数据清理
    if ($clean) {
        $model = new fbaStorageSummaryModel();
        cleanHistoryData($model);
    }
    
    echo "🎉 所有任务执行完成！\n";
}

/**
 * 显示帮助信息
 */
function showHelp()
{
    echo "FBA库存汇总统计执行脚本\n";
    echo "====================================\n";
    echo "用法:\n";
    echo "  php executeFbaSummary.php [日期] [选项]\n\n";
    echo "参数:\n";
    echo "  日期        同步日期，格式: Y-m-d，默认为今天\n\n";
    echo "选项:\n";
    echo "  --validate  执行数据完整性验证\n";
    echo "  --clean     清理历史数据\n";
    echo "  --help      显示此帮助信息\n\n";
    echo "示例:\n";
    echo "  php executeFbaSummary.php                    # 执行今天的汇总\n";
    echo "  php executeFbaSummary.php 2025-06-20        # 执行指定日期的汇总\n";
    echo "  php executeFbaSummary.php 2025-06-20 --validate --clean  # 执行汇总并验证和清理\n";
    echo "====================================\n";
}

// 检查是否请求帮助
if (isset($argv[1]) && in_array($argv[1], ['--help', '-h', 'help'])) {
    showHelp();
    exit(0);
}

// 执行主函数
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    main($argv);
}
