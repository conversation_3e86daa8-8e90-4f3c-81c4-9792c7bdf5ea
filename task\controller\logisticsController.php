<?php


namespace task\controller;

use core\lib\db\dbAfMysql;
use core\lib\db\dbErpMysql;
use core\lib\db\dbLMysql;
use plugins\logistics\models\fbaStorageSummaryModel;

class logisticsController
{
    public function __construct()
    {
        $token = $_POST['token']??'';
        if ($token != '01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0') {
            returnSuccess($_POST,'token验证失败');
        }
    }

    /**
     * FBA库存数据汇总处理
     * 分批从源数据库读取明细数据，关联店铺和listing信息后汇总到目标数据库
     * @return void
     */
    public function getFbaStorageSummary()
    {
        $startTime = microtime(true);
        $syncDate = $_POST['sync_date'] ?? date('Y-m-d');
        $page_size = 100; // 100条每批
        $redis = (new \core\lib\predisV())::$client;
        $redis_key = 'oa_fba_storage_summary';
        if ($redis->get($redis_key)) {
            $r_data = json_decode($redis->get($redis_key),true);
            $redis->expire($redis_key,0.5*60*60);
            $page = ($r_data['page']) + 1;
        } else {
            $page = 1;
        }
        $offset = ($page - 1) * 100;

        // 验证输入参数
        $this->validateSummaryParams($syncDate);

        // 实例化汇总模型
        $summaryModel = new fbaStorageSummaryModel();

        // 执行分批循环汇总处理
        $result = $this->processBatchSummaryLoop($summaryModel, $syncDate, $page, $page_size);
        $offset += $page_size;

        $endTime = microtime(true);
        $processingTime = round($endTime - $startTime, 2);

        if ($result['success']) {
            // 返回成功结果
            if ($offset >= $result['total']) {
                $redis->del($redis_key);
                SetReturn(2, "FBA库存汇总完成：处理{$result['total']}条", [
                    'sync_date' => $syncDate,
                    'total' => $result['total'],
                    'processing_time' => $processingTime,
                    'start_time' => $result['start_time'],
                    'end_time' => $result['end_time']
                ]);
            } else {
                $r_data = [
                    'page'=>$page,
                    'offset'=>$offset,
                    'total'=>$result['total'],
                    'message'=>"FBA库存汇总：已处理{$offset}条，一共{$result['total']}条",
                ];
                $redis->set($redis_key,json_encode($r_data));
                returnSuccess([],"FBA库存汇总：已处理{$offset}条，一共{$result['total']}条");
            }
        } else {
            $r_data = [
                    'page'=>$page,
                    'offset'=>$offset,
                    'total'=>$result['total'],
                    'message'=>"FBA库存汇总：已处理{$offset}条，一共{$result['total_count']}条",
                ];
                $redis->set($redis_key,json_encode($r_data));
            SetReturn(-1, "FBA库存汇总失败：" . $result['error_message'], [
                'sync_date' => $syncDate,
                'error_message' => $result['error_message'],
                'processing_time' => $processingTime
            ]);
        }
    }

    /**
     * 验证汇总参数
     * @param string $syncDate 同步日期
     * @throws \Exception
     */
    private function validateSummaryParams($syncDate)
    {
        // 验证同步日期格式
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $syncDate)) {
            throw new \Exception("同步日期格式错误，应为 YYYY-MM-DD 格式");
        }

        // 验证日期不能是未来日期
        if (strtotime($syncDate) > time()) {
            throw new \Exception("同步日期不能是未来日期");
        }
    }

    /**
     * 分批循环处理汇总逻辑
     * @param fbaStorageSummaryModel $summaryModel 汇总模型实例
     * @param string $syncDate 同步日期
     * @param int $page 当前页码
     * @param int $page_size 每页大小
     * @return array 处理结果
     */
    private function processBatchSummaryLoop($summaryModel, $syncDate, $page, $page_size)
    {
        $result = [
            'success' => false,
            'total' => 0,
            'error_message' => '',
            'start_time' => date('Y-m-d H:i:s'),
            'end_time' => '',
            'processing_time' => 0
        ];

        try {
            if ($page == 1) {
                // 清理目标表中当天的数据
                $summaryModel->clearTargetData($syncDate);
            }

            // 获取源数据总数
            $totalCount = $summaryModel->getSourceDataCount($syncDate);
            $result['total'] = $totalCount;

            if ($totalCount == 0) {
                $result['success'] = true;
                $result['end_time'] = date('Y-m-d H:i:s');
                return $result;
            }

            // 分批处理数据
            $processedCount = 0;
            $allSummaryData = [];

            // 读取一批源数据
            $batchData = $summaryModel->getSourceDataBatch($syncDate, $page, $page_size);
            $batchData = $batchData['list'];

            if (empty($batchData)) {
                return $result;
            }

            // 关联店铺和listing数据
            $enrichedData = $this->enrichBatchData($batchData);

            // 处理这批数据的四维度汇总
            $batchSummaryData = $summaryModel->processBatchSummary($enrichedData, $syncDate);

            // 合并到总汇总数据中
            $this->mergeSummaryData($allSummaryData, $batchSummaryData);

            $processedCount += count($batchData);
            $result['total_processed'] = $processedCount;
            // 批量插入所有汇总数据
            if (!empty($allSummaryData)) {
                $insertResult = $summaryModel->batchInsertSummaryData($allSummaryData);
                $result['total_inserted'] += $insertResult['inserted'];
                $result['total_updated'] += $insertResult['updated'];
            }


            $result['success'] = true;
            $result['end_time'] = date('Y-m-d H:i:s');

        } catch (\Exception $e) {
            $result['error_message'] = $e->getMessage();
        }

        return $result;
    }

    /**
     * 关联店铺和listing数据
     * @param array $batchData 批次数据
     * @return array 关联后的数据
     */
    private function enrichBatchData($batchData)
    {
        // 获取所有sid用于查询店铺信息
        $sids = array_unique(array_column($batchData, 'sid'));
        $shopData = $this->getShopDataBySids($sids);

        // 查询所有国家的code
        $countryCodes = dbAfMysql::getInstance()->table('market')->field('code,country')->list();
        $countryCodeMap = array_column($countryCodes, 'code', 'country');

        // 获取所有asin+country组合用于查询listing信息
        $asinCountryPairs = [];
        foreach ($batchData as $row) {
            $country = $shopData[$row['sid']]['country'] ?? '';
            $countryCode = $countryCodeMap[$country] ?? '';
            if (!empty($countryCode)) {
                $asinCountryPairs[] = [
                    'asin' => $row['asin'],
                    'country_code' => $countryCode
                ];
            }
        }
        $listingData = $this->getListingDataByAsinCountry($asinCountryPairs);

        // 关联数据
        $enrichedData = [];
        foreach ($batchData as $row) {
            $country = $shopData[$row['sid']]['country'] ?? '';
            $countryCode = $countryCodeMap[$country] ?? '';
            $listingKey = $row['asin'] . '|' . $countryCode;

            $enrichedRow = $row;
            $enrichedRow['country'] = $country;
            $enrichedRow['country_code'] = $countryCode;
            $enrichedRow['product_stage'] = $listingData[$listingKey]['product_stage'] ?? 0;
            $enrichedRow['product_positioning'] = $listingData[$listingKey]['product_positioning'] ?? 0;
            $enrichedRow['stock_positioning'] = $listingData[$listingKey]['stock_positioning'] ?? 0;

            $enrichedData[] = $enrichedRow;
        }

        return $enrichedData;
    }

    /**
     * 根据店铺ID获取店铺数据
     * @param array $sids 店铺ID数组
     * @return array 店铺数据
     */
    private function getShopDataBySids($sids)
    {
        if (empty($sids)) {
            return [];
        }

        $erpDb = dbErpMysql::getInstance();
        $shopList = $erpDb->table('lingxing_shop_list')->whereIn('sid', $sids)->field('sid, country')->list();

        $shopData = array_column($shopList, null, 'sid');

        return $shopData;
    }

    /**
     * 根据ASIN和国家获取listing数据
     * @param array $asinCountryPairs ASIN和国家组合数组
     * @return array listing数据
     */
    private function getListingDataByAsinCountry($asinCountryPairs)
    {
        if (empty($asinCountryPairs)) {
            return [];
        }

        $localDb = dbAfMysql::getInstance();
        $conditions = [];
        $params = [];

        foreach ($asinCountryPairs as $index => $pair) {
            $conditions[] = "(asin = :asin{$index} AND country_code = :country_code{$index})";
            $params["asin{$index}"] = $pair['asin'];
            $params["country_code{$index}"] = $pair['country_code'];
        }

        $whereClause = implode(' OR ', $conditions);
        $sql = "SELECT asin, country_code, product_stage, product_positioning, stock_positioning
                FROM listing_data
                WHERE $whereClause";

        $listingList = $localDb->queryAll($sql, $params);

        $listingData = [];
        foreach ($listingList as $listing) {
            $key = $listing['asin'] . '|' . $listing['country_code'];
            $listingData[$key] = $listing;
        }

        return $listingData;
    }

    /**
     * 合并汇总数据
     * @param array &$allSummaryData 总汇总数据（引用传递）
     * @param array $batchSummaryData 批次汇总数据
     */
    private function mergeSummaryData(&$allSummaryData, $batchSummaryData)
    {
        foreach ($batchSummaryData as $data) {
            $key = $this->generateSummaryKey($data);

            if (isset($allSummaryData[$key])) {
                // 合并数值字段
                $this->mergeNumericFields($allSummaryData[$key], $data);
                // 合并列表字段
                $this->mergeListFields($allSummaryData[$key], $data);
            } else {
                $allSummaryData[$key] = $data;
            }
        }
    }

    /**
     * 生成汇总数据的唯一键
     * @param array $data 汇总数据
     * @return string 唯一键
     */
    private function generateSummaryKey($data)
    {
        $keyParts = [$data['level_type'], $data['asin'], $data['sync_date']];

        switch ($data['level_type']) {
            case 1: // 店铺级
                $keyParts[] = $data['sku'];
                $keyParts[] = $data['country_code'];
                $keyParts[] = $data['sid'];
                break;
            case 2: // 站点级
                $keyParts[] = $data['sku'];
                $keyParts[] = $data['country_code'];
                break;
            case 3: // SKU级
                $keyParts[] = $data['sku'];
                break;
            case 4: // ASIN级
                // 只需要基本字段
                break;
        }

        return implode('|', $keyParts);
    }

    /**
     * 合并数值字段
     * @param array &$target 目标数据（引用传递）
     * @param array $source 源数据
     */
    private function mergeNumericFields(&$target, $source)
    {
        $numericFields = [
            'fba_sellable_qty', 'fba_pending_transfer_qty', 'fba_transferring_qty',
            'fba_inbound_qty', 'inventory_plus_inbound_qty',
            'fba_sellable_price', 'fba_pending_transfer_price', 'fba_transferring_price',
            'fba_inbound_price', 'inventory_plus_inbound_price'
        ];

        foreach ($numericFields as $field) {
            $target[$field] = ($target[$field] ?? 0) + ($source[$field] ?? 0);
        }
    }

    /**
     * 合并列表字段
     * @param array &$target 目标数据（引用传递）
     * @param array $source 源数据
     */
    private function mergeListFields(&$target, $source)
    {
        $listFields = ['shop_list', 'country_code_list', 'sku_list'];

        foreach ($listFields as $field) {
            $targetList = $target[$field] ?? [];
            $sourceList = $source[$field] ?? [];
            $target[$field] = array_unique(array_merge($targetList, $sourceList));
        }

        // 更新计数
    }
}
