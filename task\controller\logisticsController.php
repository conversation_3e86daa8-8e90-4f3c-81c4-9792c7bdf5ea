<?php


namespace task\controller;

use core\lib\db\dbErpMysql;
use core\lib\db\dbLMysql;
use plugins\logistics\models\fbaStorageSummaryModel;

class logisticsController
{
    public function __construct()
    {
        $token = $_POST['token']??'';
        if ($token != '01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0') {
            returnSuccess($_POST,'token验证失败');
        }
    }

    /**
     * FBA库存数据汇总处理
     * 从源数据库读取明细数据并汇总到目标数据库
     * @return void
     */
    public function getFbaStorageSummary()
    {
        $startTime = microtime(true);
        $syncDate = $_POST['sync_date'] ?? date('Y-m-d');
        $batchSize = $_POST['batch_size'] ?? 1000;

        try {
            // 验证输入参数
            $this->validateSummaryParams($syncDate, $batchSize);

            // 实例化汇总模型
            $summaryModel = new fbaStorageSummaryModel();

            // 定义进度回调函数
            $progressCallback = function($progress, $processed, $total) use ($syncDate) {
                $this->logInfo("汇总进度更新", [
                    'sync_date' => $syncDate,
                    'progress' => $progress . '%',
                    'processed' => $processed,
                    'total' => $total
                ]);
            };

            // 执行批量汇总
            $result = $summaryModel->batchSummarizeFromDetail($syncDate, $batchSize, $progressCallback);

            $endTime = microtime(true);
            $processingTime = round($endTime - $startTime, 2);

            if ($result['success']) {
                $this->logInfo("FBA库存汇总处理成功", [
                    'sync_date' => $syncDate,
                    'total_processed' => $result['total_processed'],
                    'total_inserted' => $result['total_inserted'],
                    'total_updated' => $result['total_updated'],
                    'processing_time' => $processingTime . '秒'
                ]);

                // 返回成功结果
                SetReturn(2, "FBA库存汇总完成：处理{$result['total_processed']}条，新增{$result['total_inserted']}条，更新{$result['total_updated']}条，耗时{$processingTime}秒", [
                    'sync_date' => $syncDate,
                    'total_processed' => $result['total_processed'],
                    'total_inserted' => $result['total_inserted'],
                    'total_updated' => $result['total_updated'],
                    'processing_time' => $processingTime,
                    'start_time' => $result['start_time'],
                    'end_time' => $result['end_time']
                ]);

            } else {
                $this->logError("FBA库存汇总处理失败", [
                    'sync_date' => $syncDate,
                    'error' => $result['error_message'],
                    'processing_time' => $processingTime . '秒'
                ]);

                SetReturn(-1, "FBA库存汇总失败：" . $result['error_message'], [
                    'sync_date' => $syncDate,
                    'error_message' => $result['error_message'],
                    'processing_time' => $processingTime
                ]);
            }

        } catch (\Exception $e) {
            $endTime = microtime(true);
            $processingTime = round($endTime - $startTime, 2);

            $this->logError("FBA库存汇总处理异常", [
                'sync_date' => $syncDate,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'processing_time' => $processingTime . '秒'
            ]);

            SetReturn(-1, "FBA库存汇总异常：" . $e->getMessage(), [
                'sync_date' => $syncDate,
                'error_message' => $e->getMessage(),
                'processing_time' => $processingTime
            ]);
        }
    }

    /**
     * 验证汇总参数
     * @param string $syncDate 同步日期
     * @param int $batchSize 批量大小
     * @throws \Exception
     */
    private function validateSummaryParams($syncDate, $batchSize)
    {
        // 验证同步日期格式
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $syncDate)) {
            throw new \Exception("同步日期格式错误，应为 YYYY-MM-DD 格式");
        }

        // 验证批量大小
        if (!is_numeric($batchSize) || $batchSize <= 0 || $batchSize > 10000) {
            throw new \Exception("批量大小必须为1-10000之间的数字");
        }

        // 验证日期不能是未来日期
        if (strtotime($syncDate) > time()) {
            throw new \Exception("同步日期不能是未来日期");
        }
    }

    /**
     * 记录信息日志
     * @param string $message 日志消息
     * @param array $context 上下文数据
     */
    private function logInfo($message, $context = [])
    {
        try {
            $logMessage = "[FBA汇总-INFO] $message";
            if (!empty($context)) {
                $logMessage .= " " . json_encode($context, JSON_UNESCAPED_UNICODE);
            }
            error_log($logMessage);
        } catch (\Exception $e) {
            // 日志记录失败时不影响主流程
        }
    }

    /**
     * 记录错误日志
     * @param string $message 日志消息
     * @param array $context 上下文数据
     */
    private function logError($message, $context = [])
    {
        try {
            $logMessage = "[FBA汇总-ERROR] $message";
            if (!empty($context)) {
                $logMessage .= " " . json_encode($context, JSON_UNESCAPED_UNICODE);
            }
            error_log($logMessage);
        } catch (\Exception $e) {
            // 日志记录失败时不影响主流程
        }
    }
}
