<?php
/**
 * 执行FBA库存四个维度汇总统计脚本
 * @purpose 使用PHP循环生成ASIN、ASIN+SKU、ASIN+SKU+站点、ASIN+SKU+站点+店铺四个维度的统计数据
 * @Author: System
 * @Time: 2025/06/23
 * @Usage: php executeMultiDimensionSummary.php [sync_date]
 */

// 引入核心文件
require_once __DIR__ . '/../../../core/fk.php';

use plugins\logistics\models\fbaStorageSummaryModel;

class MultiDimensionSummaryExecutor
{
    private $model;
    private $startTime;
    
    public function __construct()
    {
        $this->model = new fbaStorageSummaryModel();
        $this->startTime = microtime(true);
    }
    
    /**
     * 执行统计
     * @param string $syncDate
     */
    public function execute($syncDate = null)
    {
        if (empty($syncDate)) {
            $syncDate = date('Y-m-d');
        }
        
        echo "=== FBA库存四个维度汇总统计开始 ===\n";
        echo "同步日期: {$syncDate}\n";
        echo "开始时间: " . date('Y-m-d H:i:s') . "\n\n";
        
        try {
            // 执行统计
            echo "正在执行四个维度统计...\n";
            $result = $this->model->generateMultiDimensionSummary($syncDate);
            
            if ($result) {
                echo "✅ 统计执行成功！\n\n";
                
                // 显示统计结果
                $this->showStatistics($syncDate);
                
                // 显示执行时间
                $this->showExecutionTime();
                
            } else {
                echo "❌ 统计执行失败！\n";
                exit(1);
            }
            
        } catch (\Exception $e) {
            echo "❌ 统计执行异常: " . $e->getMessage() . "\n";
            echo "错误堆栈: " . $e->getTraceAsString() . "\n";
            exit(1);
        }
        
        echo "\n=== 统计完成 ===\n";
    }
    
    /**
     * 显示统计结果
     * @param string $syncDate
     */
    private function showStatistics($syncDate)
    {
        echo "统计结果概览:\n";
        
        $db = \core\lib\db\dbLMysql::getInstance();
        
        // 统计各维度数据量
        $levels = [
            1 => '店铺级 (ASIN+SKU+站点+店铺)',
            2 => '站点级 (ASIN+SKU+站点)',
            3 => 'SKU级 (ASIN+SKU)',
            4 => 'ASIN级 (ASIN)'
        ];
        
        $totalRecords = 0;
        foreach ($levels as $levelType => $levelName) {
            $count = $db->table('fba_storage_summary')
                ->where('level_type = :level_type AND sync_date = :sync_date AND is_deleted = 0',
                       ['level_type' => $levelType, 'sync_date' => $syncDate])
                ->count();
            
            echo sprintf("  %s: %d 条记录\n", $levelName, $count);
            $totalRecords += $count;
        }
        
        echo sprintf("  总计: %d 条记录\n\n", $totalRecords);
        
        // 显示部分详细数据
        $this->showSampleData($syncDate);
    }
    
    /**
     * 显示样本数据
     * @param string $syncDate
     */
    private function showSampleData($syncDate)
    {
        echo "样本数据展示 (每个维度显示前3条):\n\n";
        
        $db = \core\lib\db\dbLMysql::getInstance();
        
        $levels = [
            4 => 'ASIN级',
            3 => 'SKU级',
            2 => '站点级',
            1 => '店铺级'
        ];
        
        foreach ($levels as $levelType => $levelName) {
            echo "{$levelName}:\n";
            
            $data = $db->table('fba_storage_summary')
                ->field('asin, sku, site_code, sid, fba_sellable_qty, fba_sellable_price, shop_count, site_count, sku_count, shop_list, site_list, sku_list')
                ->where('level_type = :level_type AND sync_date = :sync_date AND is_deleted = 0',
                       ['level_type' => $levelType, 'sync_date' => $syncDate])
                ->order('fba_sellable_qty DESC')
                ->list(3);
            
            if (empty($data)) {
                echo "  无数据\n\n";
                continue;
            }
            
            foreach ($data as $row) {
                echo sprintf("  ASIN: %s", $row['asin']);
                
                if (!empty($row['sku'])) {
                    echo sprintf(", SKU: %s", $row['sku']);
                }
                
                if (!empty($row['site_code'])) {
                    echo sprintf(", 站点: %s", $row['site_code']);
                }
                
                if (!empty($row['sid'])) {
                    echo sprintf(", 店铺: %d", $row['sid']);
                }
                
                echo sprintf("\n    可售数量: %d, 可售价值: %.2f", 
                    $row['fba_sellable_qty'], $row['fba_sellable_price']);
                
                echo sprintf(", 店铺数: %d, 站点数: %d, SKU数: %d\n",
                    $row['shop_count'], $row['site_count'], $row['sku_count']);
                
                // 显示数组字段示例
                if ($levelType == 4) { // ASIN级显示更多信息
                    if (!empty($row['shop_list'])) {
                        $shopList = json_decode($row['shop_list'], true);
                        echo sprintf("    店铺列表: %s\n", implode(', ', $shopList));
                    }
                    
                    if (!empty($row['site_list'])) {
                        $siteList = json_decode($row['site_list'], true);
                        echo sprintf("    站点列表: %s\n", implode(', ', $siteList));
                    }
                    
                    if (!empty($row['sku_list'])) {
                        $skuList = json_decode($row['sku_list'], true);
                        echo sprintf("    SKU列表: %s\n", implode(', ', array_slice($skuList, 0, 3)) . (count($skuList) > 3 ? '...' : ''));
                    }
                }
                
                echo "\n";
            }
            
            echo "\n";
        }
    }
    
    /**
     * 显示执行时间
     */
    private function showExecutionTime()
    {
        $endTime = microtime(true);
        $executionTime = round($endTime - $this->startTime, 2);
        
        echo "执行时间: {$executionTime} 秒\n";
        echo "内存使用: " . round(memory_get_peak_usage() / 1024 / 1024, 2) . " MB\n";
    }
    
    /**
     * 显示帮助信息
     */
    public static function showHelp()
    {
        echo "FBA库存四个维度汇总统计脚本\n\n";
        echo "用法:\n";
        echo "  php executeMultiDimensionSummary.php [sync_date]\n\n";
        echo "参数:\n";
        echo "  sync_date  同步日期，格式: YYYY-MM-DD，默认为当天\n\n";
        echo "示例:\n";
        echo "  php executeMultiDimensionSummary.php                # 统计当天数据\n";
        echo "  php executeMultiDimensionSummary.php 2025-06-23     # 统计指定日期数据\n\n";
        echo "功能说明:\n";
        echo "  本脚本会从FBA库存明细数据中生成四个维度的汇总统计:\n";
        echo "  1. ASIN级别 - 按ASIN汇总\n";
        echo "  2. SKU级别 - 按ASIN+SKU汇总\n";
        echo "  3. 站点级别 - 按ASIN+SKU+站点汇总\n";
        echo "  4. 店铺级别 - 按ASIN+SKU+站点+店铺汇总\n\n";
        echo "  对于非唯一键字段，将记录为JSON数组格式。\n";
    }
}

// 处理命令行参数
if ($argc > 1) {
    if (in_array($argv[1], ['-h', '--help', 'help'])) {
        MultiDimensionSummaryExecutor::showHelp();
        exit(0);
    }
    
    $syncDate = $argv[1];
    
    // 验证日期格式
    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $syncDate)) {
        echo "错误: 日期格式不正确，应为 YYYY-MM-DD\n";
        echo "使用 'php executeMultiDimensionSummary.php --help' 查看帮助\n";
        exit(1);
    }
    
} else {
    $syncDate = null; // 使用默认值（当天）
}

// 执行统计
try {
    $executor = new MultiDimensionSummaryExecutor();
    $executor->execute($syncDate);
} catch (\Exception $e) {
    echo "脚本执行失败: " . $e->getMessage() . "\n";
    exit(1);
}
