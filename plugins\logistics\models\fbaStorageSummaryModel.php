<?php
/**
 * FBA库存汇总统计模型
 * @purpose FBA库存分层汇总统计数据处理
 * @Author: System
 * @Time: 2025/06/20
 */

namespace plugins\logistics\models;

use core\lib\db\dbLMysql;

class fbaStorageSummaryModel
{
    private $db;
    
    public function __construct()
    {
        $this->db = dbLMysql::getInstance();
    }

    /**
     * 获取汇总统计列表
     * @param array $params 查询参数
     * @return array
     */
    public function getSummaryList($params = [])
    {
        $where = 'is_deleted = 0';
        $whereData = [];
        
        // 层级筛选
        if (!empty($params['level_type'])) {
            $where .= ' AND level_type = :level_type';
            $whereData['level_type'] = $params['level_type'];
        }
        
        // ASIN筛选
        if (!empty($params['asin'])) {
            $where .= ' AND asin LIKE :asin';
            $whereData['asin'] = '%' . $params['asin'] . '%';
        }
        
        // SKU筛选
        if (!empty($params['sku'])) {
            $where .= ' AND sku LIKE :sku';
            $whereData['sku'] = '%' . $params['sku'] . '%';
        }
        
        // 站点筛选
        if (!empty($params['site_code'])) {
            $where .= ' AND site_code = :site_code';
            $whereData['site_code'] = $params['site_code'];
        }
        
        // 店铺筛选
        if (!empty($params['sid'])) {
            $where .= ' AND sid = :sid';
            $whereData['sid'] = $params['sid'];
        }
        
        // 产品阶段筛选
        if (!empty($params['product_stage'])) {
            $where .= ' AND product_stage = :product_stage';
            $whereData['product_stage'] = $params['product_stage'];
        }
        
        // 备货定位筛选
        if (!empty($params['stock_positioning'])) {
            $where .= ' AND stock_positioning = :stock_positioning';
            $whereData['stock_positioning'] = $params['stock_positioning'];
        }
        
        // 产品定位筛选
        if (!empty($params['product_positioning'])) {
            $where .= ' AND product_positioning = :product_positioning';
            $whereData['product_positioning'] = $params['product_positioning'];
        }
        
        // 同步日期筛选
        if (!empty($params['sync_date'])) {
            $where .= ' AND sync_date = :sync_date';
            $whereData['sync_date'] = $params['sync_date'];
        }
        
        $page = $params['page'] ?? 1;
        $pageSize = $params['page_size'] ?? 20;
        
        return $this->db->table('fba_storage_summary')
            ->field('*')
            ->where($where, $whereData)
            ->order('id DESC')
            ->pages($page, $pageSize);
    }

    /**
     * 获取单条汇总记录详情
     * @param int $id
     * @return array|false
     */
    public function getSummaryDetail($id)
    {
        return $this->db->table('fba_storage_summary')
            ->where('id = :id AND is_deleted = 0', ['id' => $id])
            ->one();
    }

    /**
     * 更新可编辑字段
     * @param int $id
     * @param array $data
     * @return int
     */
    public function updateEditableFields($id, $data)
    {
        $allowedFields = [
            'planned_purchase_qty',
            'purchase_days', 
            'purchase_pending_qty'
        ];
        
        $updateData = [];
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updateData[$field] = $data[$field];
            }
        }
        
        if (empty($updateData)) {
            return 0;
        }
        
        $updateData['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->table('fba_storage_summary')
            ->where('id = :id AND is_deleted = 0', ['id' => $id])
            ->update($updateData);
    }

    /**
     * 从明细数据汇总统计 - 店铺级别
     * @param string $syncDate
     * @return bool
     */
    public function summarizeByShop($syncDate)
    {
        // 先删除当日店铺级别的汇总数据
        $this->db->table('fba_storage_summary')
            ->where('level_type = 1 AND sync_date = :sync_date', ['sync_date' => $syncDate])
            ->update(['is_deleted' => 1]);
        
        // 从明细表汇总店铺级别数据
        $sql = "
            INSERT INTO oa_l_fba_storage_summary (
                level_type, asin, sku, site_code, sid, shop_name,
                product_stage, stock_positioning, product_positioning,
                fba_sellable_qty, fba_pending_transfer_qty, fba_transferring_qty, 
                fba_inbound_qty, inventory_plus_inbound_qty,
                fba_sellable_price, fba_pending_transfer_price, fba_transferring_price,
                fba_inbound_price, inventory_plus_inbound_price,
                shop_count, site_count, sku_count,
                sync_date, created_at, updated_at
            )
            SELECT 
                1 as level_type,
                d.asin,
                d.sku,
                CASE 
                    WHEN d.share_type = 1 THEN 'US' 
                    WHEN d.share_type = 2 THEN 'EU'
                    ELSE 'US'
                END as site_code,
                d.sid,
                '' as shop_name,
                COALESCE(l.product_stage, 0) as product_stage,
                COALESCE(l.stock_positioning, 0) as stock_positioning, 
                COALESCE(l.product_positioning, 0) as product_positioning,
                SUM(d.afn_fulfillable_quantity) as fba_sellable_qty,
                SUM(d.reserved_fc_transfers) as fba_pending_transfer_qty,
                SUM(d.reserved_fc_processing) as fba_transferring_qty,
                SUM(d.afn_inbound_shipped_quantity + d.afn_inbound_receiving_quantity) as fba_inbound_qty,
                SUM(d.afn_fulfillable_quantity + d.afn_inbound_shipped_quantity + d.afn_inbound_receiving_quantity) as inventory_plus_inbound_qty,
                SUM(d.afn_fulfillable_quantity_price) as fba_sellable_price,
                SUM(d.reserved_fc_transfers_price) as fba_pending_transfer_price,
                SUM(d.reserved_fc_processing_price) as fba_transferring_price,
                SUM(d.afn_inbound_shipped_quantity_price + d.afn_inbound_receiving_quantity_price) as fba_inbound_price,
                SUM(d.afn_fulfillable_quantity_price + d.afn_inbound_shipped_quantity_price + d.afn_inbound_receiving_quantity_price) as inventory_plus_inbound_price,
                1 as shop_count,
                1 as site_count,
                1 as sku_count,
                d.sync_date,
                NOW() as created_at,
                NOW() as updated_at
            FROM lingxing_fba_storage_detail d
            LEFT JOIN oa_erp_listing_data l ON d.asin = l.asin 
                AND CASE 
                    WHEN d.share_type = 1 THEN 'US' 
                    WHEN d.share_type = 2 THEN 'EU'
                    ELSE 'US'
                END = l.site_code
            WHERE d.sync_date = :sync_date AND d.is_deleted = 0
            GROUP BY d.asin, d.sku, d.sid, 
                CASE 
                    WHEN d.share_type = 1 THEN 'US' 
                    WHEN d.share_type = 2 THEN 'EU'
                    ELSE 'US'
                END
        ";
        
        $this->db->query($sql, ['sync_date' => $syncDate]);
        return true;
    }

    /**
     * 从店铺级别汇总到SKU级别
     * @param string $syncDate
     * @return bool
     */
    public function summarizeBySku($syncDate)
    {
        // 先删除当日SKU级别的汇总数据
        $this->db->table('fba_storage_summary')
            ->where('level_type = 3 AND sync_date = :sync_date', ['sync_date' => $syncDate])
            ->update(['is_deleted' => 1]);
            
        // 从店铺级别汇总到SKU级别
        $sql = "
            INSERT INTO oa_l_fba_storage_summary (
                level_type, asin, sku, site_code, sid,
                product_stage, stock_positioning, product_positioning,
                fba_sellable_qty, fba_pending_transfer_qty, fba_transferring_qty,
                fba_inbound_qty, inventory_plus_inbound_qty,
                fba_sellable_price, fba_pending_transfer_price, fba_transferring_price,
                fba_inbound_price, inventory_plus_inbound_price,
                shop_count, site_count, sku_count,
                shop_list, site_list,
                sync_date, created_at, updated_at
            )
            SELECT 
                3 as level_type,
                asin, sku, '' as site_code, 0 as sid,
                MAX(product_stage) as product_stage,
                MAX(stock_positioning) as stock_positioning,
                MAX(product_positioning) as product_positioning,
                SUM(fba_sellable_qty) as fba_sellable_qty,
                SUM(fba_pending_transfer_qty) as fba_pending_transfer_qty,
                SUM(fba_transferring_qty) as fba_transferring_qty,
                SUM(fba_inbound_qty) as fba_inbound_qty,
                SUM(inventory_plus_inbound_qty) as inventory_plus_inbound_qty,
                SUM(fba_sellable_price) as fba_sellable_price,
                SUM(fba_pending_transfer_price) as fba_pending_transfer_price,
                SUM(fba_transferring_price) as fba_transferring_price,
                SUM(fba_inbound_price) as fba_inbound_price,
                SUM(inventory_plus_inbound_price) as inventory_plus_inbound_price,
                COUNT(DISTINCT sid) as shop_count,
                COUNT(DISTINCT site_code) as site_count,
                1 as sku_count,
                JSON_ARRAYAGG(DISTINCT sid) as shop_list,
                JSON_ARRAYAGG(DISTINCT site_code) as site_list,
                sync_date,
                NOW() as created_at,
                NOW() as updated_at
            FROM oa_l_fba_storage_summary 
            WHERE level_type = 1 AND sync_date = :sync_date AND is_deleted = 0
            GROUP BY asin, sku, sync_date
        ";
        
        $this->db->query($sql, ['sync_date' => $syncDate]);
        return true;
    }

    /**
     * 从SKU级别汇总到站点级别
     * @param string $syncDate
     * @return bool
     */
    public function summarizeBySite($syncDate)
    {
        // 先删除当日站点级别的汇总数据
        $this->db->table('fba_storage_summary')
            ->where('level_type = 2 AND sync_date = :sync_date', ['sync_date' => $syncDate])
            ->update(['is_deleted' => 1]);
            
        // 从店铺级别汇总到站点级别
        $sql = "
            INSERT INTO oa_l_fba_storage_summary (
                level_type, asin, sku, site_code, sid,
                product_stage, stock_positioning, product_positioning,
                fba_sellable_qty, fba_pending_transfer_qty, fba_transferring_qty,
                fba_inbound_qty, inventory_plus_inbound_qty,
                fba_sellable_price, fba_pending_transfer_price, fba_transferring_price,
                fba_inbound_price, inventory_plus_inbound_price,
                shop_count, site_count, sku_count,
                shop_list, sku_list,
                sync_date, created_at, updated_at
            )
            SELECT 
                2 as level_type,
                asin, '' as sku, site_code, 0 as sid,
                MAX(product_stage) as product_stage,
                MAX(stock_positioning) as stock_positioning,
                MAX(product_positioning) as product_positioning,
                SUM(fba_sellable_qty) as fba_sellable_qty,
                SUM(fba_pending_transfer_qty) as fba_pending_transfer_qty,
                SUM(fba_transferring_qty) as fba_transferring_qty,
                SUM(fba_inbound_qty) as fba_inbound_qty,
                SUM(inventory_plus_inbound_qty) as inventory_plus_inbound_qty,
                SUM(fba_sellable_price) as fba_sellable_price,
                SUM(fba_pending_transfer_price) as fba_pending_transfer_price,
                SUM(fba_transferring_price) as fba_transferring_price,
                SUM(fba_inbound_price) as fba_inbound_price,
                SUM(inventory_plus_inbound_price) as inventory_plus_inbound_price,
                COUNT(DISTINCT sid) as shop_count,
                1 as site_count,
                COUNT(DISTINCT sku) as sku_count,
                JSON_ARRAYAGG(DISTINCT sid) as shop_list,
                JSON_ARRAYAGG(DISTINCT sku) as sku_list,
                sync_date,
                NOW() as created_at,
                NOW() as updated_at
            FROM oa_l_fba_storage_summary 
            WHERE level_type = 1 AND sync_date = :sync_date AND is_deleted = 0
            GROUP BY asin, site_code, sync_date
        ";
        
        $this->db->query($sql, ['sync_date' => $syncDate]);
        return true;
    }

    /**
     * 从站点级别汇总到ASIN级别
     * @param string $syncDate
     * @return bool
     */
    public function summarizeByAsin($syncDate)
    {
        // 先删除当日ASIN级别的汇总数据
        $this->db->table('fba_storage_summary')
            ->where('level_type = 4 AND sync_date = :sync_date', ['sync_date' => $syncDate])
            ->update(['is_deleted' => 1]);
            
        // 从站点级别汇总到ASIN级别
        $sql = "
            INSERT INTO oa_l_fba_storage_summary (
                level_type, asin, sku, site_code, sid,
                product_stage, stock_positioning, product_positioning,
                fba_sellable_qty, fba_pending_transfer_qty, fba_transferring_qty,
                fba_inbound_qty, inventory_plus_inbound_qty,
                fba_sellable_price, fba_pending_transfer_price, fba_transferring_price,
                fba_inbound_price, inventory_plus_inbound_price,
                shop_count, site_count, sku_count,
                shop_list, site_list, sku_list,
                sync_date, created_at, updated_at
            )
            SELECT 
                4 as level_type,
                asin, '' as sku, '' as site_code, 0 as sid,
                MAX(product_stage) as product_stage,
                MAX(stock_positioning) as stock_positioning,
                MAX(product_positioning) as product_positioning,
                SUM(fba_sellable_qty) as fba_sellable_qty,
                SUM(fba_pending_transfer_qty) as fba_pending_transfer_qty,
                SUM(fba_transferring_qty) as fba_transferring_qty,
                SUM(fba_inbound_qty) as fba_inbound_qty,
                SUM(inventory_plus_inbound_qty) as inventory_plus_inbound_qty,
                SUM(fba_sellable_price) as fba_sellable_price,
                SUM(fba_pending_transfer_price) as fba_pending_transfer_price,
                SUM(fba_transferring_price) as fba_transferring_price,
                SUM(fba_inbound_price) as fba_inbound_price,
                SUM(inventory_plus_inbound_price) as inventory_plus_inbound_price,
                COUNT(DISTINCT CONCAT(sid, '-', site_code)) as shop_count,
                COUNT(DISTINCT site_code) as site_count,
                COUNT(DISTINCT sku) as sku_count,
                JSON_ARRAYAGG(DISTINCT sid) as shop_list,
                JSON_ARRAYAGG(DISTINCT site_code) as site_list,
                JSON_ARRAYAGG(DISTINCT sku) as sku_list,
                sync_date,
                NOW() as created_at,
                NOW() as updated_at
            FROM oa_l_fba_storage_summary 
            WHERE level_type = 1 AND sync_date = :sync_date AND is_deleted = 0
            GROUP BY asin, sync_date
        ";
        
        $this->db->query($sql, ['sync_date' => $syncDate]);
        return true;
    }

    /**
     * 执行完整的汇总统计流程
     * @param string $syncDate
     * @return bool
     */
    public function executeFullSummary($syncDate = null)
    {
        if (empty($syncDate)) {
            $syncDate = date('Y-m-d');
        }
        
        try {
            $this->db->beginTransaction();
            
            // 按层级依次汇总
            $this->summarizeByShop($syncDate);
            $this->summarizeBySku($syncDate);
            $this->summarizeBySite($syncDate);
            $this->summarizeByAsin($syncDate);
            
            $this->db->commit();
            return true;
        } catch (\Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /**
     * 使用PHP循环生成四个维度的统计数据
     * @param string $syncDate 同步日期，默认为当天
     * @return bool
     */
    public function generateMultiDimensionSummary($syncDate = null)
    {
        if (empty($syncDate)) {
            $syncDate = date('Y-m-d');
        }
        
        try {
            $this->db->beginTransaction();
            
            // 先清除当天的汇总数据
            $this->clearSummaryData($syncDate);
            
            // 读取当天同步的明细数据
            $detailData = $this->getDetailDataForSummary($syncDate);
            
            if (empty($detailData)) {
                $this->db->commit();
                return true;
            }
            
            // 生成四个维度的汇总数据
            $summaryData = $this->processDimensionData($detailData, $syncDate);
            
            // 批量插入汇总数据
            $this->batchInsertSummaryData($summaryData);
            
            $this->db->commit();
            return true;
        } catch (\Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /**
     * 清除指定日期的汇总数据
     * @param string $syncDate
     * @return void
     */
    private function clearSummaryData($syncDate)
    {
        $this->db->table('fba_storage_summary')
            ->where('sync_date = :sync_date', ['sync_date' => $syncDate])
            ->update(['is_deleted' => 1]);
    }

    /**
     * 获取明细数据用于汇总
     * @param string $syncDate
     * @return array
     */
    private function getDetailDataForSummary($syncDate)
    {
        $sql = "
            SELECT 
                d.asin,
                d.sku,
                d.sid,
                CASE 
                    WHEN d.share_type = 1 THEN 'US' 
                    WHEN d.share_type = 2 THEN 'EU'
                    ELSE 'US'
                END as site_code,
                
                -- listing配置信息
                COALESCE(l.product_stage, 0) as product_stage,
                COALESCE(l.stock_positioning, 0) as stock_positioning, 
                COALESCE(l.product_positioning, 0) as product_positioning,
                
                -- 库存数量
                d.afn_fulfillable_quantity as fba_sellable_qty,
                d.reserved_fc_transfers as fba_pending_transfer_qty,
                d.reserved_fc_processing as fba_transferring_qty,
                (d.afn_inbound_shipped_quantity + d.afn_inbound_receiving_quantity) as fba_inbound_qty,
                (d.afn_fulfillable_quantity + d.afn_inbound_shipped_quantity + d.afn_inbound_receiving_quantity) as inventory_plus_inbound_qty,
                
                -- 成本价格
                d.afn_fulfillable_quantity_price as fba_sellable_price,
                d.reserved_fc_transfers_price as fba_pending_transfer_price,
                d.reserved_fc_processing_price as fba_transferring_price,
                (d.afn_inbound_shipped_quantity_price + d.afn_inbound_receiving_quantity_price) as fba_inbound_price,
                (d.afn_fulfillable_quantity_price + d.afn_inbound_shipped_quantity_price + d.afn_inbound_receiving_quantity_price) as inventory_plus_inbound_price,
                
                -- 其他字段
                d.product_name,
                d.name as warehouse_name,
                d.seller_sku,
                d.fnsku,
                d.category_text,
                d.product_brand_text
                
            FROM lingxing_fba_storage_detail d
            LEFT JOIN oa_erp_listing_data l ON d.asin = l.asin 
                AND CASE 
                    WHEN d.share_type = 1 THEN 'US' 
                    WHEN d.share_type = 2 THEN 'EU'
                    ELSE 'US'
                END = l.site_code
            WHERE d.sync_date = :sync_date AND d.is_deleted = 0
            ORDER BY d.asin, d.sku, d.sid
        ";
        
        return $this->db->queryAll($sql, ['sync_date' => $syncDate]);
    }

    /**
     * 处理四个维度的数据
     * @param array $detailData
     * @param string $syncDate
     * @return array
     */
    private function processDimensionData($detailData, $syncDate)
    {
        $dimensions = [
            1 => [], // 店铺级：asin+sku+site_code+sid
            2 => [], // 站点级：asin+sku+site_code
            3 => [], // SKU级：asin+sku
            4 => []  // ASIN级：asin
        ];
        
        foreach ($detailData as $row) {
            // 1. 店铺级维度 (asin+sku+site_code+sid)
            $shopKey = $row['asin'] . '|' . $row['sku'] . '|' . $row['site_code'] . '|' . $row['sid'];
            if (!isset($dimensions[1][$shopKey])) {
                $dimensions[1][$shopKey] = $this->initDimensionData(1, $row, $syncDate);
            }
            $this->aggregateData($dimensions[1][$shopKey], $row);
            
            // 2. 站点级维度 (asin+sku+site_code)
            $siteKey = $row['asin'] . '|' . $row['sku'] . '|' . $row['site_code'];
            if (!isset($dimensions[2][$siteKey])) {
                $dimensions[2][$siteKey] = $this->initDimensionData(2, $row, $syncDate);
            }
            $this->aggregateData($dimensions[2][$siteKey], $row);
            $this->addToArrayField($dimensions[2][$siteKey], 'shop_list', $row['sid']);
            
            // 3. SKU级维度 (asin+sku)
            $skuKey = $row['asin'] . '|' . $row['sku'];
            if (!isset($dimensions[3][$skuKey])) {
                $dimensions[3][$skuKey] = $this->initDimensionData(3, $row, $syncDate);
            }
            $this->aggregateData($dimensions[3][$skuKey], $row);
            $this->addToArrayField($dimensions[3][$skuKey], 'shop_list', $row['sid']);
            $this->addToArrayField($dimensions[3][$skuKey], 'site_list', $row['site_code']);
            
            // 4. ASIN级维度 (asin)
            $asinKey = $row['asin'];
            if (!isset($dimensions[4][$asinKey])) {
                $dimensions[4][$asinKey] = $this->initDimensionData(4, $row, $syncDate);
            }
            $this->aggregateData($dimensions[4][$asinKey], $row);
            $this->addToArrayField($dimensions[4][$asinKey], 'shop_list', $row['sid']);
            $this->addToArrayField($dimensions[4][$asinKey], 'site_list', $row['site_code']);
            $this->addToArrayField($dimensions[4][$asinKey], 'sku_list', $row['sku']);
            $this->addToArrayField($dimensions[4][$asinKey], 'warehouse_list', $row['warehouse_name']);
            $this->addToArrayField($dimensions[4][$asinKey], 'seller_sku_list', $row['seller_sku']);
            $this->addToArrayField($dimensions[4][$asinKey], 'fnsku_list', $row['fnsku']);
            $this->addToArrayField($dimensions[4][$asinKey], 'category_list', $row['category_text']);
            $this->addToArrayField($dimensions[4][$asinKey], 'brand_list', $row['product_brand_text']);
        }
        
        // 最终处理：转换数组字段为JSON，计算统计数据
        return $this->finalizeDimensionData($dimensions);
    }

    /**
     * 初始化维度数据结构
     * @param int $levelType
     * @param array $row
     * @param string $syncDate
     * @return array
     */
    private function initDimensionData($levelType, $row, $syncDate)
    {
        $data = [
            'level_type' => $levelType,
            'asin' => $row['asin'],
            'sku' => '',
            'site_code' => '',
            'sid' => 0,
            'shop_name' => '',
            'product_stage' => $row['product_stage'],
            'stock_positioning' => $row['stock_positioning'],
            'product_positioning' => $row['product_positioning'],
            'fba_sellable_qty' => 0,
            'fba_pending_transfer_qty' => 0,
            'fba_transferring_qty' => 0,
            'fba_inbound_qty' => 0,
            'inventory_plus_inbound_qty' => 0,
            'fba_sellable_price' => 0.00,
            'fba_pending_transfer_price' => 0.00,
            'fba_transferring_price' => 0.00,
            'fba_inbound_price' => 0.00,
            'inventory_plus_inbound_price' => 0.00,
            'shop_count' => 0,
            'site_count' => 0,
            'sku_count' => 0,
            'shop_list' => [],
            'site_list' => [],
            'sku_list' => [],
            'warehouse_list' => [],
            'seller_sku_list' => [],
            'fnsku_list' => [],
            'category_list' => [],
            'brand_list' => [],
            'sync_date' => $syncDate,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'is_deleted' => 0
        ];
        
        // 根据维度设置相应字段
        switch ($levelType) {
            case 1: // 店铺级
                $data['sku'] = $row['sku'];
                $data['site_code'] = $row['site_code'];
                $data['sid'] = $row['sid'];
                $data['shop_count'] = 1;
                $data['site_count'] = 1;
                $data['sku_count'] = 1;
                break;
            case 2: // 站点级
                $data['sku'] = $row['sku'];
                $data['site_code'] = $row['site_code'];
                $data['sku_count'] = 1;
                break;
            case 3: // SKU级
                $data['sku'] = $row['sku'];
                break;
            case 4: // ASIN级
                break;
        }
        
        return $data;
    }

    /**
     * 聚合数据
     * @param array &$target
     * @param array $row
     * @return void
     */
    private function aggregateData(&$target, $row)
    {
        // 聚合数量字段
        $target['fba_sellable_qty'] += $row['fba_sellable_qty'];
        $target['fba_pending_transfer_qty'] += $row['fba_pending_transfer_qty'];
        $target['fba_transferring_qty'] += $row['fba_transferring_qty'];
        $target['fba_inbound_qty'] += $row['fba_inbound_qty'];
        $target['inventory_plus_inbound_qty'] += $row['inventory_plus_inbound_qty'];
        
        // 聚合价格字段
        $target['fba_sellable_price'] += $row['fba_sellable_price'];
        $target['fba_pending_transfer_price'] += $row['fba_pending_transfer_price'];
        $target['fba_transferring_price'] += $row['fba_transferring_price'];
        $target['fba_inbound_price'] += $row['fba_inbound_price'];
        $target['inventory_plus_inbound_price'] += $row['inventory_plus_inbound_price'];
        
        // 更新配置信息为最大值（取最严格的配置）
        $target['product_stage'] = max($target['product_stage'], $row['product_stage']);
        $target['stock_positioning'] = max($target['stock_positioning'], $row['stock_positioning']);
        $target['product_positioning'] = max($target['product_positioning'], $row['product_positioning']);
    }

    /**
     * 向数组字段添加元素
     * @param array &$target
     * @param string $field
     * @param mixed $value
     * @return void
     */
    private function addToArrayField(&$target, $field, $value)
    {
        if (!empty($value) && !in_array($value, $target[$field])) {
            $target[$field][] = $value;
        }
    }

    /**
     * 最终处理维度数据
     * @param array $dimensions
     * @return array
     */
    private function finalizeDimensionData($dimensions)
    {
        $result = [];
        
        foreach ($dimensions as $levelType => $levelData) {
            foreach ($levelData as $data) {
                // 计算统计数量
                if ($levelType > 1) {
                    $data['shop_count'] = count($data['shop_list']);
                }
                if ($levelType > 2) {
                    $data['site_count'] = count($data['site_list']);
                }
                if ($levelType > 3) {
                    $data['sku_count'] = count($data['sku_list']);
                }
                
                // 转换数组字段为JSON格式
                $arrayFields = ['shop_list', 'site_list', 'sku_list', 'warehouse_list', 
                               'seller_sku_list', 'fnsku_list', 'category_list', 'brand_list'];
                
                foreach ($arrayFields as $field) {
                    if (isset($data[$field]) && is_array($data[$field])) {
                        if (empty($data[$field])) {
                            $data[$field] = null;
                        } else {
                            $data[$field] = json_encode(array_unique($data[$field]), JSON_UNESCAPED_UNICODE);
                        }
                    }
                }
                
                $result[] = $data;
            }
        }
        
        return $result;
    }

    /**
     * 批量插入汇总数据
     * @param array $summaryData
     * @return void
     */
    private function batchInsertSummaryData($summaryData)
    {
        if (empty($summaryData)) {
            return;
        }
        
        // 定义字段顺序
        $fields = [
            'level_type', 'asin', 'sku', 'site_code', 'sid', 'shop_name',
            'product_stage', 'stock_positioning', 'product_positioning',
            'fba_sellable_qty', 'fba_pending_transfer_qty', 'fba_transferring_qty',
            'fba_inbound_qty', 'inventory_plus_inbound_qty',
            'fba_sellable_price', 'fba_pending_transfer_price', 'fba_transferring_price',
            'fba_inbound_price', 'inventory_plus_inbound_price',
            'shop_count', 'site_count', 'sku_count',
            'shop_list', 'site_list', 'sku_list', 'warehouse_list',
            'seller_sku_list', 'fnsku_list', 'category_list', 'brand_list',
            'sync_date', 'created_at', 'updated_at', 'is_deleted'
        ];
        
        // 准备批量插入的数据
        $batchData = [];
        foreach ($summaryData as $row) {
            $rowData = [];
            foreach ($fields as $field) {
                $rowData[] = $row[$field] ?? null;
            }
            $batchData[] = $rowData;
        }
        
        // 批量插入
        $this->db->table('fba_storage_summary')->insertBatch($fields, $batchData);
    }

    /**
     * 获取层级类型映射
     * @return array
     */
    public function getLevelTypeMap()
    {
        return [
            1 => '店铺级',
            2 => '站点级', 
            3 => 'SKU级',
            4 => 'ASIN级'
        ];
    }

    /**
     * 获取产品阶段映射
     * @return array
     */
    public function getProductStageMap()
    {
        return [
            1 => '成长期',
            2 => '稳定期',
            3 => '衰退期',
            4 => '新品期',
            5 => '清货'
        ];
    }

    /**
     * 获取备货定位映射
     * @return array
     */
    public function getStockPositioningMap()
    {
        return [
            1 => '重点备货',
            2 => '常规备货',
            3 => '停止备货'
        ];
    }

    /**
     * 获取产品定位映射
     * @return array
     */
    public function getProductPositioningMap()
    {
        return [
            1 => '头部产品',
            2 => '腰部产品',
            3 => '尾部产品',
            4 => '清货产品'
        ];
    }
}
