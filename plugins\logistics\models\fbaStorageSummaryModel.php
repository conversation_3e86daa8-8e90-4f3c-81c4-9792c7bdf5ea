<?php
/**
 * FBA库存汇总统计模型
 * @purpose FBA库存分层汇总统计数据处理
 * @Author: System
 * @Time: 2025/06/20
 */

namespace plugins\logistics\models;

use core\lib\db\dbLMysql;
use core\lib\db\dbErpMysql;
use core\lib\log;

class fbaStorageSummaryModel
{
    private $db;
    
    public function __construct()
    {
        $this->db = dbLMysql::getInstance();
    }

    /**
     * 获取汇总统计列表
     * @param array $params 查询参数
     * @return array
     */
    public function getSummaryList($params = [])
    {
        $where = '1=1';
        $whereData = [];
        
        // 层级筛选
        if (!empty($params['level_type'])) {
            $where .= ' AND level_type = :level_type';
            $whereData['level_type'] = $params['level_type'];
        }
        
        // ASIN筛选
        if (!empty($params['asin'])) {
            $where .= ' AND asin LIKE :asin';
            $whereData['asin'] = '%' . $params['asin'] . '%';
        }
        
        // SKU筛选
        if (!empty($params['sku'])) {
            $where .= ' AND sku LIKE :sku';
            $whereData['sku'] = '%' . $params['sku'] . '%';
        }
        
        // 站点筛选
        if (!empty($params['site_code'])) {
            $where .= ' AND site_code = :site_code';
            $whereData['site_code'] = $params['site_code'];
        }
        
        // 店铺筛选
        if (!empty($params['sid'])) {
            $where .= ' AND sid = :sid';
            $whereData['sid'] = $params['sid'];
        }
        
        // 产品阶段筛选
        if (!empty($params['product_stage'])) {
            $where .= ' AND product_stage = :product_stage';
            $whereData['product_stage'] = $params['product_stage'];
        }
        
        // 备货定位筛选
        if (!empty($params['stock_positioning'])) {
            $where .= ' AND stock_positioning = :stock_positioning';
            $whereData['stock_positioning'] = $params['stock_positioning'];
        }
        
        // 产品定位筛选
        if (!empty($params['product_positioning'])) {
            $where .= ' AND product_positioning = :product_positioning';
            $whereData['product_positioning'] = $params['product_positioning'];
        }
        
        // 同步日期筛选
        if (!empty($params['sync_date'])) {
            $where .= ' AND sync_date = :sync_date';
            $whereData['sync_date'] = $params['sync_date'];
        }
        
        $page = $params['page'] ?? 1;
        $pageSize = $params['page_size'] ?? 20;
        
        return $this->db->table('fba_storage_summary')
            ->field('*')
            ->where($where, $whereData)
            ->order('id DESC')
            ->pages($page, $pageSize);
    }

    /**
     * 获取单条汇总记录详情
     * @param int $id
     * @return array|false
     */
    public function getSummaryDetail($id)
    {
        return $this->db->table('fba_storage_summary')
            ->where('id = :id', ['id' => $id])
            ->one();
    }

    /**
     * 更新可编辑字段
     * @param int $id
     * @param array $data
     * @return int
     */
    public function updateEditableFields($id, $data)
    {
        $allowedFields = [
            'planned_purchase_qty',
            'purchase_days', 
            'purchase_pending_qty'
        ];
        
        $updateData = [];
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updateData[$field] = $data[$field];
            }
        }
        
        if (empty($updateData)) {
            return 0;
        }
        
        $updateData['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->table('fba_storage_summary')
            ->where('id = :id ', ['id' => $id])
            ->update($updateData);
    }

    /**
     * 从明细数据汇总统计 - 店铺级别
     * @param string $syncDate
     * @return bool
     */
    public function summarizeByShop($syncDate)
    {
        // 先删除当日店铺级别的汇总数据
        $this->db->table('fba_storage_summary')
            ->where('level_type = 1 AND sync_date = :sync_date', ['sync_date' => $syncDate])
            ->delete();
        
        // 从明细表汇总店铺级别数据
        $sql = "
            INSERT INTO oa_l_fba_storage_summary (
                level_type, asin, sku, site_code, sid, shop_name,
                product_stage, stock_positioning, product_positioning,
                fba_sellable_qty, fba_pending_transfer_qty, fba_transferring_qty, 
                fba_inbound_qty, inventory_plus_inbound_qty,
                fba_sellable_price, fba_pending_transfer_price, fba_transferring_price,
                fba_inbound_price, inventory_plus_inbound_price,
                shop_count, site_count, sku_count,
                sync_date, created_at, updated_at
            )
            SELECT 
                1 as level_type,
                d.asin,
                d.sku,
                CASE 
                    WHEN d.share_type = 1 THEN 'US' 
                    WHEN d.share_type = 2 THEN 'EU'
                    ELSE 'US'
                END as site_code,
                d.sid,
                '' as shop_name,
                COALESCE(l.product_stage, 0) as product_stage,
                COALESCE(l.stock_positioning, 0) as stock_positioning, 
                COALESCE(l.product_positioning, 0) as product_positioning,
                SUM(d.afn_fulfillable_quantity) as fba_sellable_qty,
                SUM(d.reserved_fc_transfers) as fba_pending_transfer_qty,
                SUM(d.reserved_fc_processing) as fba_transferring_qty,
                SUM(d.afn_inbound_shipped_quantity + d.afn_inbound_receiving_quantity) as fba_inbound_qty,
                SUM(d.afn_fulfillable_quantity + d.afn_inbound_shipped_quantity + d.afn_inbound_receiving_quantity) as inventory_plus_inbound_qty,
                SUM(d.afn_fulfillable_quantity_price) as fba_sellable_price,
                SUM(d.reserved_fc_transfers_price) as fba_pending_transfer_price,
                SUM(d.reserved_fc_processing_price) as fba_transferring_price,
                SUM(d.afn_inbound_shipped_quantity_price + d.afn_inbound_receiving_quantity_price) as fba_inbound_price,
                SUM(d.afn_fulfillable_quantity_price + d.afn_inbound_shipped_quantity_price + d.afn_inbound_receiving_quantity_price) as inventory_plus_inbound_price,
                1 as shop_count,
                1 as site_count,
                1 as sku_count,
                d.sync_date,
                NOW() as created_at,
                NOW() as updated_at
            FROM lingxing_fba_storage_detail d
            LEFT JOIN oa_erp_listing_data l ON d.asin = l.asin 
                AND CASE 
                    WHEN d.share_type = 1 THEN 'US' 
                    WHEN d.share_type = 2 THEN 'EU'
                    ELSE 'US'
                END = l.site_code
            WHERE d.sync_date = :sync_date
            GROUP BY d.asin, d.sku, d.sid, 
                CASE 
                    WHEN d.share_type = 1 THEN 'US' 
                    WHEN d.share_type = 2 THEN 'EU'
                    ELSE 'US'
                END
        ";
        
        $this->db->query($sql, ['sync_date' => $syncDate]);
        return true;
    }

    /**
     * 从店铺级别汇总到SKU级别
     * @param string $syncDate
     * @return bool
     */
    public function summarizeBySku($syncDate)
    {
        // 先删除当日SKU级别的汇总数据
        $this->db->table('fba_storage_summary')
            ->where('level_type = 3 AND sync_date = :sync_date', ['sync_date' => $syncDate])
            ->delete();
            
        // 从店铺级别汇总到SKU级别
        $sql = "
            INSERT INTO oa_l_fba_storage_summary (
                level_type, asin, sku, site_code, sid,
                product_stage, stock_positioning, product_positioning,
                fba_sellable_qty, fba_pending_transfer_qty, fba_transferring_qty,
                fba_inbound_qty, inventory_plus_inbound_qty,
                fba_sellable_price, fba_pending_transfer_price, fba_transferring_price,
                fba_inbound_price, inventory_plus_inbound_price,
                shop_count, site_count, sku_count,
                shop_list, site_list,
                sync_date, created_at, updated_at
            )
            SELECT 
                3 as level_type,
                asin, sku, '' as site_code, 0 as sid,
                MAX(product_stage) as product_stage,
                MAX(stock_positioning) as stock_positioning,
                MAX(product_positioning) as product_positioning,
                SUM(fba_sellable_qty) as fba_sellable_qty,
                SUM(fba_pending_transfer_qty) as fba_pending_transfer_qty,
                SUM(fba_transferring_qty) as fba_transferring_qty,
                SUM(fba_inbound_qty) as fba_inbound_qty,
                SUM(inventory_plus_inbound_qty) as inventory_plus_inbound_qty,
                SUM(fba_sellable_price) as fba_sellable_price,
                SUM(fba_pending_transfer_price) as fba_pending_transfer_price,
                SUM(fba_transferring_price) as fba_transferring_price,
                SUM(fba_inbound_price) as fba_inbound_price,
                SUM(inventory_plus_inbound_price) as inventory_plus_inbound_price,
                COUNT(DISTINCT sid) as shop_count,
                COUNT(DISTINCT site_code) as site_count,
                1 as sku_count,
                JSON_ARRAYAGG(DISTINCT sid) as shop_list,
                JSON_ARRAYAGG(DISTINCT site_code) as site_list,
                sync_date,
                NOW() as created_at,
                NOW() as updated_at
            FROM oa_l_fba_storage_summary 
            WHERE level_type = 1 AND sync_date = :sync_date 
            GROUP BY asin, sku, sync_date
        ";
        
        $this->db->query($sql, ['sync_date' => $syncDate]);
        return true;
    }

    /**
     * 从SKU级别汇总到站点级别
     * @param string $syncDate
     * @return bool
     */
    public function summarizeBySite($syncDate)
    {
        // 先删除当日站点级别的汇总数据
        $this->db->table('fba_storage_summary')
            ->where('level_type = 2 AND sync_date = :sync_date', ['sync_date' => $syncDate])
            ->delete();
            
        // 从店铺级别汇总到站点级别
        $sql = "
            INSERT INTO oa_l_fba_storage_summary (
                level_type, asin, sku, site_code, sid,
                product_stage, stock_positioning, product_positioning,
                fba_sellable_qty, fba_pending_transfer_qty, fba_transferring_qty,
                fba_inbound_qty, inventory_plus_inbound_qty,
                fba_sellable_price, fba_pending_transfer_price, fba_transferring_price,
                fba_inbound_price, inventory_plus_inbound_price,
                shop_count, site_count, sku_count,
                shop_list, sku_list,
                sync_date, created_at, updated_at
            )
            SELECT 
                2 as level_type,
                asin, '' as sku, site_code, 0 as sid,
                MAX(product_stage) as product_stage,
                MAX(stock_positioning) as stock_positioning,
                MAX(product_positioning) as product_positioning,
                SUM(fba_sellable_qty) as fba_sellable_qty,
                SUM(fba_pending_transfer_qty) as fba_pending_transfer_qty,
                SUM(fba_transferring_qty) as fba_transferring_qty,
                SUM(fba_inbound_qty) as fba_inbound_qty,
                SUM(inventory_plus_inbound_qty) as inventory_plus_inbound_qty,
                SUM(fba_sellable_price) as fba_sellable_price,
                SUM(fba_pending_transfer_price) as fba_pending_transfer_price,
                SUM(fba_transferring_price) as fba_transferring_price,
                SUM(fba_inbound_price) as fba_inbound_price,
                SUM(inventory_plus_inbound_price) as inventory_plus_inbound_price,
                COUNT(DISTINCT sid) as shop_count,
                1 as site_count,
                COUNT(DISTINCT sku) as sku_count,
                JSON_ARRAYAGG(DISTINCT sid) as shop_list,
                JSON_ARRAYAGG(DISTINCT sku) as sku_list,
                sync_date,
                NOW() as created_at,
                NOW() as updated_at
            FROM oa_l_fba_storage_summary 
            WHERE level_type = 1 AND sync_date = :sync_date 
            GROUP BY asin, site_code, sync_date
        ";
        
        $this->db->query($sql, ['sync_date' => $syncDate]);
        return true;
    }

    /**
     * 从站点级别汇总到ASIN级别
     * @param string $syncDate
     * @return bool
     */
    public function summarizeByAsin($syncDate)
    {
        // 先删除当日ASIN级别的汇总数据
        $this->db->table('fba_storage_summary')
            ->where('level_type = 4 AND sync_date = :sync_date', ['sync_date' => $syncDate])
            ->delete();
            
        // 从站点级别汇总到ASIN级别
        $sql = "
            INSERT INTO oa_l_fba_storage_summary (
                level_type, asin, sku, site_code, sid,
                product_stage, stock_positioning, product_positioning,
                fba_sellable_qty, fba_pending_transfer_qty, fba_transferring_qty,
                fba_inbound_qty, inventory_plus_inbound_qty,
                fba_sellable_price, fba_pending_transfer_price, fba_transferring_price,
                fba_inbound_price, inventory_plus_inbound_price,
                shop_count, site_count, sku_count,
                shop_list, site_list, sku_list,
                sync_date, created_at, updated_at
            )
            SELECT 
                4 as level_type,
                asin, '' as sku, '' as site_code, 0 as sid,
                MAX(product_stage) as product_stage,
                MAX(stock_positioning) as stock_positioning,
                MAX(product_positioning) as product_positioning,
                SUM(fba_sellable_qty) as fba_sellable_qty,
                SUM(fba_pending_transfer_qty) as fba_pending_transfer_qty,
                SUM(fba_transferring_qty) as fba_transferring_qty,
                SUM(fba_inbound_qty) as fba_inbound_qty,
                SUM(inventory_plus_inbound_qty) as inventory_plus_inbound_qty,
                SUM(fba_sellable_price) as fba_sellable_price,
                SUM(fba_pending_transfer_price) as fba_pending_transfer_price,
                SUM(fba_transferring_price) as fba_transferring_price,
                SUM(fba_inbound_price) as fba_inbound_price,
                SUM(inventory_plus_inbound_price) as inventory_plus_inbound_price,
                COUNT(DISTINCT CONCAT(sid, '-', site_code)) as shop_count,
                COUNT(DISTINCT site_code) as site_count,
                COUNT(DISTINCT sku) as sku_count,
                JSON_ARRAYAGG(DISTINCT sid) as shop_list,
                JSON_ARRAYAGG(DISTINCT site_code) as site_list,
                JSON_ARRAYAGG(DISTINCT sku) as sku_list,
                sync_date,
                NOW() as created_at,
                NOW() as updated_at
            FROM oa_l_fba_storage_summary 
            WHERE level_type = 1 AND sync_date = :sync_date
            GROUP BY asin, sync_date
        ";
        
        $this->db->query($sql, ['sync_date' => $syncDate]);
        return true;
    }

    /**
     * 执行完整的汇总统计流程
     * @param string $syncDate
     * @return bool
     */
    public function executeFullSummary($syncDate = null)
    {
        if (empty($syncDate)) {
            $syncDate = date('Y-m-d');
        }
        
        try {
            $this->db->beginTransaction();
            
            // 按层级依次汇总
            $this->summarizeByShop($syncDate);
            $this->summarizeBySku($syncDate);
            $this->summarizeBySite($syncDate);
            $this->summarizeByAsin($syncDate);
            
            $this->db->commit();
            return true;
        } catch (\Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /**
     * 使用PHP循环生成四个维度的统计数据
     * @param string $syncDate 同步日期，默认为当天
     * @return bool
     */
    public function generateMultiDimensionSummary($syncDate = null)
    {
        if (empty($syncDate)) {
            $syncDate = date('Y-m-d');
        }
        
        try {
            $this->db->beginTransaction();
            
            // 先清除当天的汇总数据
            $this->clearSummaryData($syncDate);
            
            // 读取当天同步的明细数据
            $detailData = $this->getDetailDataForSummary($syncDate);
            
            if (empty($detailData)) {
                $this->db->commit();
                return true;
            }
            
            // 生成四个维度的汇总数据
            $summaryData = $this->processDimensionData($detailData, $syncDate);
            
            // 批量插入汇总数据
            $this->batchInsertSummaryData($summaryData);
            
            $this->db->commit();
            return true;
        } catch (\Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /**
     * 清除指定日期的汇总数据
     * @param string $syncDate
     * @return void
     */
    private function clearSummaryData($syncDate)
    {
        $this->db->table('fba_storage_summary')
            ->where('sync_date = :sync_date', ['sync_date' => $syncDate])
            ->delete();
    }

    /**
     * 获取明细数据用于汇总
     * @param string $syncDate
     * @return array
     */
    private function getDetailDataForSummary($syncDate)
    {
        $sql = "
            SELECT 
                d.asin,
                d.sku,
                d.sid,
                CASE 
                    WHEN d.share_type = 1 THEN 'US' 
                    WHEN d.share_type = 2 THEN 'EU'
                    ELSE 'US'
                END as site_code,
                
                -- listing配置信息
                COALESCE(l.product_stage, 0) as product_stage,
                COALESCE(l.stock_positioning, 0) as stock_positioning, 
                COALESCE(l.product_positioning, 0) as product_positioning,
                
                -- 库存数量
                d.afn_fulfillable_quantity as fba_sellable_qty,
                d.reserved_fc_transfers as fba_pending_transfer_qty,
                d.reserved_fc_processing as fba_transferring_qty,
                (d.afn_inbound_shipped_quantity + d.afn_inbound_receiving_quantity) as fba_inbound_qty,
                (d.afn_fulfillable_quantity + d.afn_inbound_shipped_quantity + d.afn_inbound_receiving_quantity) as inventory_plus_inbound_qty,
                
                -- 成本价格
                d.afn_fulfillable_quantity_price as fba_sellable_price,
                d.reserved_fc_transfers_price as fba_pending_transfer_price,
                d.reserved_fc_processing_price as fba_transferring_price,
                (d.afn_inbound_shipped_quantity_price + d.afn_inbound_receiving_quantity_price) as fba_inbound_price,
                (d.afn_fulfillable_quantity_price + d.afn_inbound_shipped_quantity_price + d.afn_inbound_receiving_quantity_price) as inventory_plus_inbound_price,
                
                -- 其他字段
                d.product_name,
                d.name as warehouse_name,
                d.seller_sku,
                d.fnsku,
                d.category_text,
                d.product_brand_text
                
            FROM lingxing_fba_storage_detail d
            LEFT JOIN oa_erp_listing_data l ON d.asin = l.asin 
                AND CASE 
                    WHEN d.share_type = 1 THEN 'US' 
                    WHEN d.share_type = 2 THEN 'EU'
                    ELSE 'US'
                END = l.site_code
            WHERE d.sync_date = :sync_date
            ORDER BY d.asin, d.sku, d.sid
        ";
        
        return $this->db->queryAll($sql, ['sync_date' => $syncDate]);
    }

    /**
     * 处理四个维度的数据
     * @param array $detailData
     * @param string $syncDate
     * @return array
     */
    private function processDimensionData($detailData, $syncDate)
    {
        $dimensions = [
            1 => [], // 店铺级：asin+sku+site_code+sid
            2 => [], // 站点级：asin+sku+site_code
            3 => [], // SKU级：asin+sku
            4 => []  // ASIN级：asin
        ];
        
        foreach ($detailData as $row) {
            // 1. 店铺级维度 (asin+sku+site_code+sid)
            $shopKey = $row['asin'] . '|' . $row['sku'] . '|' . $row['site_code'] . '|' . $row['sid'];
            if (!isset($dimensions[1][$shopKey])) {
                $dimensions[1][$shopKey] = $this->initDimensionData(1, $row, $syncDate);
            }
            $this->aggregateData($dimensions[1][$shopKey], $row);
            
            // 2. 站点级维度 (asin+sku+site_code)
            $siteKey = $row['asin'] . '|' . $row['sku'] . '|' . $row['site_code'];
            if (!isset($dimensions[2][$siteKey])) {
                $dimensions[2][$siteKey] = $this->initDimensionData(2, $row, $syncDate);
            }
            $this->aggregateData($dimensions[2][$siteKey], $row);
            $this->addToArrayField($dimensions[2][$siteKey], 'shop_list', $row['sid']);
            
            // 3. SKU级维度 (asin+sku)
            $skuKey = $row['asin'] . '|' . $row['sku'];
            if (!isset($dimensions[3][$skuKey])) {
                $dimensions[3][$skuKey] = $this->initDimensionData(3, $row, $syncDate);
            }
            $this->aggregateData($dimensions[3][$skuKey], $row);
            $this->addToArrayField($dimensions[3][$skuKey], 'shop_list', $row['sid']);
            $this->addToArrayField($dimensions[3][$skuKey], 'site_list', $row['site_code']);
            
            // 4. ASIN级维度 (asin)
            $asinKey = $row['asin'];
            if (!isset($dimensions[4][$asinKey])) {
                $dimensions[4][$asinKey] = $this->initDimensionData(4, $row, $syncDate);
            }
            $this->aggregateData($dimensions[4][$asinKey], $row);
            $this->addToArrayField($dimensions[4][$asinKey], 'shop_list', $row['sid']);
            $this->addToArrayField($dimensions[4][$asinKey], 'site_list', $row['site_code']);
            $this->addToArrayField($dimensions[4][$asinKey], 'sku_list', $row['sku']);
            $this->addToArrayField($dimensions[4][$asinKey], 'warehouse_list', $row['warehouse_name']);
            $this->addToArrayField($dimensions[4][$asinKey], 'seller_sku_list', $row['seller_sku']);
            $this->addToArrayField($dimensions[4][$asinKey], 'fnsku_list', $row['fnsku']);
            $this->addToArrayField($dimensions[4][$asinKey], 'category_list', $row['category_text']);
            $this->addToArrayField($dimensions[4][$asinKey], 'brand_list', $row['product_brand_text']);
        }
        
        // 最终处理：转换数组字段为JSON，计算统计数据
        return $this->finalizeDimensionData($dimensions);
    }

    /**
     * 初始化维度数据结构
     * @param int $levelType
     * @param array $row
     * @param string $syncDate
     * @return array
     */
    private function initDimensionData($levelType, $row, $syncDate)
    {
        $data = [
            'level_type' => $levelType,
            'asin' => $row['asin'],
            'sku' => '',
            'site_code' => '',
            'sid' => 0,
            'shop_name' => '',
            'product_stage' => $row['product_stage'],
            'stock_positioning' => $row['stock_positioning'],
            'product_positioning' => $row['product_positioning'],
            'fba_sellable_qty' => 0,
            'fba_pending_transfer_qty' => 0,
            'fba_transferring_qty' => 0,
            'fba_inbound_qty' => 0,
            'inventory_plus_inbound_qty' => 0,
            'fba_sellable_price' => 0.00,
            'fba_pending_transfer_price' => 0.00,
            'fba_transferring_price' => 0.00,
            'fba_inbound_price' => 0.00,
            'inventory_plus_inbound_price' => 0.00,
            'shop_count' => 0,
            'site_count' => 0,
            'sku_count' => 0,
            'shop_list' => [],
            'site_list' => [],
            'sku_list' => [],
            'warehouse_list' => [],
            'seller_sku_list' => [],
            'fnsku_list' => [],
            'category_list' => [],
            'brand_list' => [],
            'sync_date' => $syncDate,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        // 根据维度设置相应字段
        switch ($levelType) {
            case 1: // 店铺级
                $data['sku'] = $row['sku'];
                $data['site_code'] = $row['site_code'];
                $data['sid'] = $row['sid'];
                $data['shop_count'] = 1;
                $data['site_count'] = 1;
                $data['sku_count'] = 1;
                break;
            case 2: // 站点级
                $data['sku'] = $row['sku'];
                $data['site_code'] = $row['site_code'];
                $data['sku_count'] = 1;
                break;
            case 3: // SKU级
                $data['sku'] = $row['sku'];
                break;
            case 4: // ASIN级
                break;
        }
        
        return $data;
    }

    /**
     * 聚合数据
     * @param array &$target
     * @param array $row
     * @return void
     */
    private function aggregateData(&$target, $row)
    {
        // 聚合数量字段
        $target['fba_sellable_qty'] += $row['fba_sellable_qty'];
        $target['fba_pending_transfer_qty'] += $row['fba_pending_transfer_qty'];
        $target['fba_transferring_qty'] += $row['fba_transferring_qty'];
        $target['fba_inbound_qty'] += $row['fba_inbound_qty'];
        $target['inventory_plus_inbound_qty'] += $row['inventory_plus_inbound_qty'];
        
        // 聚合价格字段
        $target['fba_sellable_price'] += $row['fba_sellable_price'];
        $target['fba_pending_transfer_price'] += $row['fba_pending_transfer_price'];
        $target['fba_transferring_price'] += $row['fba_transferring_price'];
        $target['fba_inbound_price'] += $row['fba_inbound_price'];
        $target['inventory_plus_inbound_price'] += $row['inventory_plus_inbound_price'];
        
        // 更新配置信息为最大值（取最严格的配置）
        $target['product_stage'] = max($target['product_stage'], $row['product_stage']);
        $target['stock_positioning'] = max($target['stock_positioning'], $row['stock_positioning']);
        $target['product_positioning'] = max($target['product_positioning'], $row['product_positioning']);
    }

    /**
     * 向数组字段添加元素
     * @param array &$target
     * @param string $field
     * @param mixed $value
     * @return void
     */
    private function addToArrayField(&$target, $field, $value)
    {
        if (!empty($value) && !in_array($value, $target[$field])) {
            $target[$field][] = $value;
        }
    }

    /**
     * 最终处理维度数据
     * @param array $dimensions
     * @return array
     */
    private function finalizeDimensionData($dimensions)
    {
        $result = [];
        
        foreach ($dimensions as $levelType => $levelData) {
            foreach ($levelData as $data) {
                // 计算统计数量
                if ($levelType > 1) {
                    $data['shop_count'] = count($data['shop_list']);
                }
                if ($levelType > 2) {
                    $data['site_count'] = count($data['site_list']);
                }
                if ($levelType > 3) {
                    $data['sku_count'] = count($data['sku_list']);
                }
                
                // 转换数组字段为JSON格式
                $arrayFields = ['shop_list', 'site_list', 'sku_list', 'warehouse_list', 
                               'seller_sku_list', 'fnsku_list', 'category_list', 'brand_list'];
                
                foreach ($arrayFields as $field) {
                    if (isset($data[$field]) && is_array($data[$field])) {
                        if (empty($data[$field])) {
                            $data[$field] = null;
                        } else {
                            $data[$field] = json_encode(array_unique($data[$field]), JSON_UNESCAPED_UNICODE);
                        }
                    }
                }
                
                $result[] = $data;
            }
        }
        
        return $result;
    }

    /**
     * 批量插入汇总数据
     * @param array $summaryData
     * @return void
     */
    private function batchInsertSummaryData($summaryData)
    {
        if (empty($summaryData)) {
            return;
        }
        
        // 定义字段顺序
        $fields = [
            'level_type', 'asin', 'sku', 'site_code', 'sid', 'shop_name',
            'product_stage', 'stock_positioning', 'product_positioning',
            'fba_sellable_qty', 'fba_pending_transfer_qty', 'fba_transferring_qty',
            'fba_inbound_qty', 'inventory_plus_inbound_qty',
            'fba_sellable_price', 'fba_pending_transfer_price', 'fba_transferring_price',
            'fba_inbound_price', 'inventory_plus_inbound_price',
            'shop_count', 'site_count', 'sku_count',
            'shop_list', 'site_list', 'sku_list', 'warehouse_list',
            'seller_sku_list', 'fnsku_list', 'category_list', 'brand_list',
            'sync_date', 'created_at', 'updated_at'
        ];
        
        // 准备批量插入的数据
        $batchData = [];
        foreach ($summaryData as $row) {
            $rowData = [];
            foreach ($fields as $field) {
                $rowData[] = $row[$field] ?? null;
            }
            $batchData[] = $rowData;
        }
        
        // 批量插入
        $this->db->table('fba_storage_summary')->insertBatch($fields, $batchData);
    }

    /**
     * 获取层级类型映射
     * @return array
     */
    public function getLevelTypeMap()
    {
        return [
            1 => '店铺级',
            2 => '站点级', 
            3 => 'SKU级',
            4 => 'ASIN级'
        ];
    }

    /**
     * 获取产品阶段映射
     * @return array
     */
    public function getProductStageMap()
    {
        return [
            1 => '成长期',
            2 => '稳定期',
            3 => '衰退期',
            4 => '新品期',
            5 => '清货'
        ];
    }

    /**
     * 获取备货定位映射
     * @return array
     */
    public function getStockPositioningMap()
    {
        return [
            1 => '重点备货',
            2 => '常规备货',
            3 => '停止备货'
        ];
    }

    /**
     * 获取产品定位映射
     * @return array
     */
    public function getProductPositioningMap()
    {
        return [
            1 => '头部产品',
            2 => '腰部产品',
            3 => '尾部产品',
            4 => '清货产品'
        ];
    }

    /**
     * 批量数据汇总 - 从源数据库读取并汇总到目标数据库
     * @param string $syncDate 同步日期，默认为当天
     * @param int $batchSize 批量处理大小，默认1000条
     * @param callable|null $progressCallback 进度回调函数
     * @return array 返回处理结果统计
     */
    public function batchSummarizeFromDetail($syncDate = null, $batchSize = 1000, $progressCallback = null)
    {
        if (empty($syncDate)) {
            $syncDate = date('Y-m-d');
        }

        // 验证输入参数
        $this->validateInputParams($syncDate, $batchSize);

        $sourceDb = dbErpMysql::getInstance();
        $targetDb = $this->db;

        // 验证数据库连接
        $this->validateDbConnection($sourceDb, 'ERP数据库');
        $this->validateDbConnection($targetDb, '目标数据库');

        $result = [
            'success' => false,
            'total_processed' => 0,
            'total_inserted' => 0,
            'total_updated' => 0,
            'error_message' => '',
            'start_time' => date('Y-m-d H:i:s'),
            'end_time' => '',
            'processing_time' => 0
        ];

        try {
            $startTime = microtime(true);

            // 记录开始日志
            $this->logInfo("开始批量汇总处理", [
                'sync_date' => $syncDate,
                'batch_size' => $batchSize,
                'start_time' => $result['start_time']
            ]);

            // 开始事务
            $targetDb->beginTransaction();

            // 获取源数据总数
            $totalCount = $this->getSourceDataCount($sourceDb, $syncDate);
            $this->logInfo("获取源数据总数", ['total_count' => $totalCount]);

            if ($totalCount == 0) {
                $targetDb->commit();
                $result['success'] = true;
                $result['end_time'] = date('Y-m-d H:i:s');
                $this->logInfo("无源数据需要处理，汇总完成");
                return $result;
            }

            // 清理目标表中当天的数据
            $this->clearTargetData($targetDb, $syncDate);
            $this->logInfo("清理目标表数据完成", ['sync_date' => $syncDate]);

            // 分批处理数据
            $offset = 0;
            $processedCount = 0;

            while ($offset < $totalCount) {
                // 检查内存使用情况
                if (!$this->checkMemoryLimit()) {
                    throw new \Exception("内存使用超限，停止处理");
                }

                // 读取一批源数据
                $batchData = $this->safeDbOperation(
                    function() use ($sourceDb, $syncDate, $offset, $batchSize) {
                        return $this->getSourceDataBatch($sourceDb, $syncDate, $offset, $batchSize);
                    },
                    "读取源数据批次"
                );

                if (empty($batchData)) {
                    $this->logInfo("批次数据为空，结束处理", ['offset' => $offset]);
                    break;
                }

                // 处理这批数据的四维度汇总
                $summaryData = $this->processBatchSummary($batchData, $syncDate);

                // 批量插入汇总数据
                $insertResult = $this->safeDbOperation(
                    function() use ($targetDb, $summaryData) {
                        return $this->batchInsertSummaryDataNew($targetDb, $summaryData);
                    },
                    "批量插入汇总数据"
                );

                $processedCount += count($batchData);
                $result['total_processed'] = $processedCount;
                $result['total_inserted'] += $insertResult['inserted'];
                $result['total_updated'] += $insertResult['updated'];

                // 记录批次处理日志
                $this->logInfo("批次处理完成", [
                    'batch_size' => count($batchData),
                    'processed_count' => $processedCount,
                    'total_count' => $totalCount,
                    'inserted' => $insertResult['inserted'],
                    'updated' => $insertResult['updated'],
                    'memory_usage' => $this->getMemoryUsage()
                ]);

                // 调用进度回调
                if ($progressCallback && is_callable($progressCallback)) {
                    $progress = round(($processedCount / $totalCount) * 100, 2);
                    $progressCallback($progress, $processedCount, $totalCount);
                }

                $offset += $batchSize;
            }

            $targetDb->commit();

            $endTime = microtime(true);
            $result['success'] = true;
            $result['end_time'] = date('Y-m-d H:i:s');
            $result['processing_time'] = round($endTime - $startTime, 2);

            $this->logInfo("批量汇总处理完成", [
                'total_processed' => $result['total_processed'],
                'total_inserted' => $result['total_inserted'],
                'total_updated' => $result['total_updated'],
                'processing_time' => $result['processing_time'] . '秒',
                'final_memory_usage' => $this->getMemoryUsage()
            ]);

        } catch (\Exception $e) {
            $targetDb->rollBack();
            $result['error_message'] = $e->getMessage();

            $this->logError("FBA汇总处理失败", [
                'sync_date' => $syncDate,
                'batch_size' => $batchSize,
                'processed_count' => $result['total_processed'],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        return $result;
    }

    /**
     * 获取源数据总数
     * @param object $sourceDb 源数据库连接
     * @param string $syncDate 同步日期
     * @return int
     */
    private function getSourceDataCount($sourceDb, $syncDate)
    {
        $sql = "SELECT COUNT(*) as total FROM lingxing_fba_storage_detail
                WHERE sync_date = :sync_date";

        $result = $sourceDb->query($sql, ['sync_date' => $syncDate]);
        return $result ? (int)$result['total'] : 0;
    }

    /**
     * 清理目标表中当天的数据
     * @param object $targetDb 目标数据库连接
     * @param string $syncDate 同步日期
     * @return void
     */
    private function clearTargetData($targetDb, $syncDate)
    {
        $targetDb->table('fba_storage_summary')
            ->where('sync_date = :sync_date', ['sync_date' => $syncDate])
            ->delete();
    }

    /**
     * 分批获取源数据
     * @param object $sourceDb 源数据库连接
     * @param string $syncDate 同步日期
     * @param int $offset 偏移量
     * @param int $limit 限制数量
     * @return array
     */
    private function getSourceDataBatch($sourceDb, $syncDate, $offset, $limit)
    {
        $sql = "SELECT
                    d.id, d.sid, d.asin, d.sku, d.seller_sku, d.fnsku,
                    d.product_name, d.product_brand_text, d.category_text,
                    d.afn_fulfillable_quantity, d.reserved_fc_transfers, d.reserved_fc_processing,
                    d.afn_inbound_shipped_quantity, d.afn_inbound_receiving_quantity,
                    d.afn_fulfillable_quantity_price, d.reserved_fc_transfers_price,
                    d.reserved_fc_processing_price, d.afn_inbound_shipped_quantity_price,
                    d.afn_inbound_receiving_quantity_price, d.share_type,
                    CASE
                        WHEN d.share_type = 1 THEN 'US'
                        WHEN d.share_type = 2 THEN 'EU'
                        ELSE 'US'
                    END as site_code,
                    l.product_stage, l.stock_positioning, l.product_positioning,
                    s.shop_name
                FROM lingxing_fba_storage_detail d
                LEFT JOIN oa_erp_listing_data l ON d.asin = l.asin
                    AND CASE
                        WHEN d.share_type = 1 THEN 'US'
                        WHEN d.share_type = 2 THEN 'EU'
                        ELSE 'US'
                    END = l.site_code
                LEFT JOIN oa_erp_shop s ON d.sid = s.id
                WHERE d.sync_date = :sync_date
                ORDER BY d.asin, d.sku, d.sid
                LIMIT :offset, :limit";

        return $sourceDb->queryAll($sql, [
            'sync_date' => $syncDate,
            'offset' => $offset,
            'limit' => $limit
        ]);
    }

    /**
     * 处理批量数据的四维度汇总
     * @param array $batchData 批量数据
     * @param string $syncDate 同步日期
     * @return array 四维度汇总数据
     */
    private function processBatchSummary($batchData, $syncDate)
    {
        $dimensions = [
            1 => [], // 店铺级：asin+sku+site_code+sid
            2 => [], // 站点级：asin+sku+site_code
            3 => [], // SKU级：asin+sku
            4 => []  // ASIN级：asin
        ];

        foreach ($batchData as $row) {
            // 计算各种数量和价格
            $quantities = $this->calculateQuantities($row);
            $prices = $this->calculatePrices($row);

            // 维度1：店铺级 (asin+sku+site_code+sid)
            $key1 = $row['asin'] . '|' . $row['sku'] . '|' . $row['site_code'] . '|' . $row['sid'];
            if (!isset($dimensions[1][$key1])) {
                $dimensions[1][$key1] = $this->initDimensionData(1, $row, $syncDate);
            }
            $this->aggregateDataNew($dimensions[1][$key1], $quantities, $prices, $row);

            // 维度2：站点级 (asin+sku+site_code)
            $key2 = $row['asin'] . '|' . $row['sku'] . '|' . $row['site_code'];
            if (!isset($dimensions[2][$key2])) {
                $dimensions[2][$key2] = $this->initDimensionData(2, $row, $syncDate);
            }
            $this->aggregateDataNew($dimensions[2][$key2], $quantities, $prices, $row);

            // 维度3：SKU级 (asin+sku)
            $key3 = $row['asin'] . '|' . $row['sku'];
            if (!isset($dimensions[3][$key3])) {
                $dimensions[3][$key3] = $this->initDimensionData(3, $row, $syncDate);
            }
            $this->aggregateDataNew($dimensions[3][$key3], $quantities, $prices, $row);

            // 维度4：ASIN级 (asin)
            $key4 = $row['asin'];
            if (!isset($dimensions[4][$key4])) {
                $dimensions[4][$key4] = $this->initDimensionData(4, $row, $syncDate);
            }
            $this->aggregateDataNew($dimensions[4][$key4], $quantities, $prices, $row);
        }

        // 合并所有维度数据
        $result = [];
        foreach ($dimensions as $levelType => $levelData) {
            foreach ($levelData as $data) {
                $result[] = $data;
            }
        }

        return $result;
    }

    /**
     * 计算数量相关字段
     * @param array $row 数据行
     * @return array
     */
    private function calculateQuantities($row)
    {
        return [
            'fba_sellable_qty' => (int)($row['afn_fulfillable_quantity'] ?? 0),
            'fba_pending_transfer_qty' => (int)($row['reserved_fc_transfers'] ?? 0),
            'fba_transferring_qty' => (int)($row['reserved_fc_processing'] ?? 0),
            'fba_inbound_qty' => (int)(($row['afn_inbound_shipped_quantity'] ?? 0) + ($row['afn_inbound_receiving_quantity'] ?? 0)),
            'inventory_plus_inbound_qty' => (int)(($row['afn_fulfillable_quantity'] ?? 0) + ($row['afn_inbound_shipped_quantity'] ?? 0) + ($row['afn_inbound_receiving_quantity'] ?? 0))
        ];
    }

    /**
     * 计算价格相关字段
     * @param array $row 数据行
     * @return array
     */
    private function calculatePrices($row)
    {
        return [
            'fba_sellable_price' => (float)($row['afn_fulfillable_quantity_price'] ?? 0),
            'fba_pending_transfer_price' => (float)($row['reserved_fc_transfers_price'] ?? 0),
            'fba_transferring_price' => (float)($row['reserved_fc_processing_price'] ?? 0),
            'fba_inbound_price' => (float)(($row['afn_inbound_shipped_quantity_price'] ?? 0) + ($row['afn_inbound_receiving_quantity_price'] ?? 0)),
            'inventory_plus_inbound_price' => (float)(($row['afn_fulfillable_quantity_price'] ?? 0) + ($row['afn_inbound_shipped_quantity_price'] ?? 0) + ($row['afn_inbound_receiving_quantity_price'] ?? 0))
        ];
    }

    /**
     * 聚合数据到维度记录中（新版本）
     * @param array &$dimensionData 维度数据（引用传递）
     * @param array $quantities 数量数据
     * @param array $prices 价格数据
     * @param array $row 原始数据行
     * @return void
     */
    private function aggregateDataNew(&$dimensionData, $quantities, $prices, $row)
    {
        // 累加数量字段
        foreach ($quantities as $field => $value) {
            $dimensionData[$field] += $value;
        }

        // 累加价格字段
        foreach ($prices as $field => $value) {
            $dimensionData[$field] += $value;
        }

        // 更新统计信息
        $this->updateStatisticsNew($dimensionData, $row);
    }

    /**
     * 更新统计信息（新版本）
     * @param array &$dimensionData 维度数据（引用传递）
     * @param array $row 原始数据行
     * @return void
     */
    private function updateStatisticsNew(&$dimensionData, $row)
    {
        // 更新店铺列表
        if (!empty($row['sid']) && !in_array($row['sid'], $dimensionData['shop_list'])) {
            $dimensionData['shop_list'][] = $row['sid'];
        }

        // 更新站点列表
        if (!empty($row['site_code']) && !in_array($row['site_code'], $dimensionData['site_list'])) {
            $dimensionData['site_list'][] = $row['site_code'];
        }

        // 更新SKU列表
        if (!empty($row['sku']) && !in_array($row['sku'], $dimensionData['sku_list'])) {
            $dimensionData['sku_list'][] = $row['sku'];
        }

        // 更新计数
        $dimensionData['shop_count'] = count($dimensionData['shop_list']);
        $dimensionData['site_count'] = count($dimensionData['site_list']);
        $dimensionData['sku_count'] = count($dimensionData['sku_list']);

        // 更新时间
        $dimensionData['updated_at'] = date('Y-m-d H:i:s');
    }

    /**
     * 批量插入汇总数据到目标数据库（新版本）
     * @param object $targetDb 目标数据库连接
     * @param array $summaryData 汇总数据
     * @return array 插入结果统计
     */
    private function batchInsertSummaryDataNew($targetDb, $summaryData)
    {
        $result = ['inserted' => 0, 'updated' => 0];

        foreach ($summaryData as $data) {
            // 转换数组字段为JSON
            $data['shop_list'] = json_encode($data['shop_list'], JSON_UNESCAPED_UNICODE);
            $data['site_list'] = json_encode($data['site_list'], JSON_UNESCAPED_UNICODE);
            $data['sku_list'] = json_encode($data['sku_list'], JSON_UNESCAPED_UNICODE);

            try {
                // 检查是否已存在相同维度的记录
                $existingId = $this->checkExistingRecord($targetDb, $data);

                if ($existingId) {
                    // 更新现有记录
                    $this->updateExistingRecord($targetDb, $existingId, $data);
                    $result['updated']++;
                } else {
                    // 插入新记录
                    $targetDb->table('fba_storage_summary')->insert($data);
                    $result['inserted']++;
                }
            } catch (\Exception $e) {
                error_log("插入汇总数据失败: " . $e->getMessage() . " Data: " . json_encode($data));
                throw $e;
            }
        }

        return $result;
    }



    /**
     * 检查是否存在相同维度的记录
     * @param object $targetDb 目标数据库连接
     * @param array $data 数据
     * @return int|false 存在则返回ID，否则返回false
     */
    private function checkExistingRecord($targetDb, $data)
    {
        $where = 'level_type = :level_type AND asin = :asin AND sync_date = :sync_date ';
        $whereData = [
            'level_type' => $data['level_type'],
            'asin' => $data['asin'],
            'sync_date' => $data['sync_date']
        ];

        // 根据层级添加额外的唯一性条件
        switch ($data['level_type']) {
            case 1: // 店铺级
                $where .= ' AND sku = :sku AND site_code = :site_code AND sid = :sid';
                $whereData['sku'] = $data['sku'];
                $whereData['site_code'] = $data['site_code'];
                $whereData['sid'] = $data['sid'];
                break;
            case 2: // 站点级
                $where .= ' AND sku = :sku AND site_code = :site_code';
                $whereData['sku'] = $data['sku'];
                $whereData['site_code'] = $data['site_code'];
                break;
            case 3: // SKU级
                $where .= ' AND sku = :sku';
                $whereData['sku'] = $data['sku'];
                break;
            case 4: // ASIN级
                // 只需要asin条件
                break;
        }

        $existing = $targetDb->table('fba_storage_summary')
            ->field('id')
            ->where($where, $whereData)
            ->one();

        return $existing ? $existing['id'] : false;
    }

    /**
     * 更新现有记录
     * @param object $targetDb 目标数据库连接
     * @param int $id 记录ID
     * @param array $data 新数据
     * @return void
     */
    private function updateExistingRecord($targetDb, $id, $data)
    {
        // 获取现有记录
        $existing = $targetDb->table('fba_storage_summary')
            ->where('id = :id', ['id' => $id])
            ->one();

        if (!$existing) {
            return;
        }

        // 累加数值字段
        $numericFields = [
            'fba_sellable_qty', 'fba_pending_transfer_qty', 'fba_transferring_qty',
            'fba_inbound_qty', 'inventory_plus_inbound_qty',
            'fba_sellable_price', 'fba_pending_transfer_price', 'fba_transferring_price',
            'fba_inbound_price', 'inventory_plus_inbound_price'
        ];

        $updateData = [];
        foreach ($numericFields as $field) {
            $updateData[$field] = (float)$existing[$field] + (float)$data[$field];
        }

        // 合并列表字段
        $listFields = ['shop_list', 'site_list', 'sku_list'];
        foreach ($listFields as $field) {
            $existingList = json_decode($existing[$field] ?? '[]', true) ?: [];
            $newList = json_decode($data[$field] ?? '[]', true) ?: [];
            $mergedList = array_unique(array_merge($existingList, $newList));
            $updateData[$field] = json_encode($mergedList, JSON_UNESCAPED_UNICODE);
        }

        // 更新计数字段
        $updateData['shop_count'] = count(json_decode($updateData['shop_list'], true));
        $updateData['site_count'] = count(json_decode($updateData['site_list'], true));
        $updateData['sku_count'] = count(json_decode($updateData['sku_list'], true));

        // 更新时间
        $updateData['updated_at'] = date('Y-m-d H:i:s');

        // 执行更新
        $targetDb->table('fba_storage_summary')
            ->where('id = :id', ['id' => $id])
            ->update($updateData);
    }

    /**
     * 记录信息日志
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return void
     */
    private function logInfo($message, $context = [])
    {
        try {
            error_log("[FBA汇总-INFO] $message " . (!empty($context) ? json_encode($context, JSON_UNESCAPED_UNICODE) : ''));
        } catch (\Exception $e) {
            // 日志记录失败时不影响主流程
        }
    }

    /**
     * 记录错误日志
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return void
     */
    private function logError($message, $context = [])
    {
        try {
            error_log("[FBA汇总-ERROR] $message " . (!empty($context) ? json_encode($context, JSON_UNESCAPED_UNICODE) : ''));
        } catch (\Exception $e) {
            // 日志记录失败时不影响主流程
        }
    }

    /**
     * 验证数据库连接
     * @param object $db 数据库连接对象
     * @param string $dbName 数据库名称
     * @return bool
     */
    private function validateDbConnection($db, $dbName)
    {
        try {
            if (!$db) {
                throw new \Exception("$dbName 数据库连接失败");
            }
            return true;
        } catch (\Exception $e) {
            $this->logError("数据库连接验证失败", ['db_name' => $dbName, 'error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * 验证输入参数
     * @param string $syncDate 同步日期
     * @param int $batchSize 批量大小
     * @return void
     * @throws \Exception
     */
    private function validateInputParams($syncDate, $batchSize)
    {
        // 验证同步日期格式
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $syncDate)) {
            throw new \Exception("同步日期格式错误，应为 YYYY-MM-DD 格式");
        }

        // 验证批量大小
        if (!is_int($batchSize) || $batchSize <= 0 || $batchSize > 10000) {
            throw new \Exception("批量大小必须为1-10000之间的整数");
        }

        // 验证日期不能是未来日期
        if (strtotime($syncDate) > time()) {
            throw new \Exception("同步日期不能是未来日期");
        }
    }

    /**
     * 安全执行数据库操作
     * @param callable $operation 数据库操作函数
     * @param string $operationName 操作名称
     * @param int $maxRetries 最大重试次数
     * @return mixed
     * @throws \Exception
     */
    private function safeDbOperation($operation, $operationName, $maxRetries = 3)
    {
        $retries = 0;
        $lastException = null;

        while ($retries < $maxRetries) {
            try {
                return $operation();
            } catch (\Exception $e) {
                $lastException = $e;
                $retries++;

                $this->logError("数据库操作失败，准备重试", [
                    'operation' => $operationName,
                    'retry' => $retries,
                    'max_retries' => $maxRetries,
                    'error' => $e->getMessage()
                ]);

                if ($retries < $maxRetries) {
                    // 等待一段时间后重试
                    usleep(100000 * $retries); // 递增等待时间
                }
            }
        }

        // 所有重试都失败了
        $this->logError("数据库操作最终失败", [
            'operation' => $operationName,
            'total_retries' => $retries,
            'final_error' => $lastException->getMessage()
        ]);

        throw new \Exception("$operationName 操作失败: " . $lastException->getMessage());
    }

    /**
     * 获取内存使用情况
     * @return array
     */
    private function getMemoryUsage()
    {
        return [
            'current' => round(memory_get_usage() / 1024 / 1024, 2) . 'MB',
            'peak' => round(memory_get_peak_usage() / 1024 / 1024, 2) . 'MB'
        ];
    }

    /**
     * 检查内存使用是否超限
     * @param int $limitMB 内存限制（MB）
     * @return bool
     */
    private function checkMemoryLimit($limitMB = 512)
    {
        $currentUsage = memory_get_usage() / 1024 / 1024;
        if ($currentUsage > $limitMB) {
            $this->logError("内存使用超限", [
                'current_usage' => round($currentUsage, 2) . 'MB',
                'limit' => $limitMB . 'MB'
            ]);
            return false;
        }
        return true;
    }
}
