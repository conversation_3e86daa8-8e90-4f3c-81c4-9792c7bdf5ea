<?php
/**
 * FBA库存汇总功能集成测试
 * 测试控制器方法是否能正常工作
 */

// 设置基本环境
define('SELF_FK', realpath('')); 
define('CORE', SELF_FK.'/core'); 
define('DEBUG', true); 
define('LOG_PATH', SELF_FK.'/log'); 
define('SYS_PUBLISH',false); 
define('ROOT', SELF_FK."/");

require_once "environment.php";
require_once ROOT."/vendor/autoload.php";
include CORE.'/common/function.php';
include CORE.'/common/return.php';
include CORE.'/common/phperror.php';
include CORE.'/fk.php';

spl_autoload_register('\core\fk::load');

use task\controller\logisticsController;

echo "====================================\n";
echo "FBA库存汇总功能集成测试\n";
echo "====================================\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n\n";

// 模拟POST参数
$_POST = [
    'token' => '01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0',
    'sync_date' => date('Y-m-d'),
    'batch_size' => 100  // 使用小批量进行测试
];

echo "测试参数:\n";
echo "- Token: " . $_POST['token'] . "\n";
echo "- 同步日期: " . $_POST['sync_date'] . "\n";
echo "- 批量大小: " . $_POST['batch_size'] . "\n\n";

try {
    echo "🔄 开始测试控制器方法...\n";
    
    // 实例化控制器
    $controller = new logisticsController();
    
    // 捕获输出
    ob_start();
    
    // 调用汇总方法
    $controller->getFbaStorageSummary();
    
    // 获取输出
    $output = ob_get_clean();
    
    echo "✅ 控制器方法调用成功\n";
    echo "输出内容: $output\n";
    
} catch (Exception $e) {
    echo "❌ 控制器方法调用失败\n";
    echo "错误信息: " . $e->getMessage() . "\n";
    echo "错误堆栈: " . $e->getTraceAsString() . "\n";
}

echo "\n====================================\n";
echo "测试完成时间: " . date('Y-m-d H:i:s') . "\n";
echo "====================================\n";
