# FBA库存汇总统计模块

## 📋 **功能概述**

### 🚧 FBA库存汇总统计代码施工方案 🚧

**施工等级**: 🟡 MINOR - 新增FBA库存汇总统计功能模块  
**施工紧迫性**: 正常优先级 - 提升库存管理效率  
**工程量**: 4个任务，涉及5个文件，预计8工时

### :bullseye: 施工目标
1. 设计分层汇总的数据库表结构，支持店铺→站点→SKU→ASIN四级汇总
2. 实现从lingxing_fba_storage_detail明细表到汇总表的数据统计
3. 提供完整的API接口，支持列表查询、详情查看、编辑功能
4. 增加计划采购、采购未交等可编辑字段，满足业务需求
5. 完成全面的单元测试，确保功能稳定性

### :world_map: 施工策略
```mermaid
graph TD
    A[明细数据lingxing_fba_storage_detail] --> B[店铺级汇总level_type=1]
    B --> C[SKU级汇总level_type=3]
    B --> D[站点级汇总level_type=2]
    C --> E[ASIN级汇总level_type=4]
    D --> E
    
    F[listing_data配置] --> B
    G[API接口] --> H[列表查询]
    G --> I[详情查看]
    G --> J[编辑功能]
    G --> K[批量编辑]
    G --> L[执行汇总]
```

### :high_voltage: 实施计划

| 阶段 | 优先级 | 任务数 | 关键里程碑 | 风险评估 | 预计工时 |
|------|--------|--------|------------|----------|----------|
| **数据库设计** | 🔴 P0 | 1个 | 汇总表结构完成 | 🟢低 | 1小时 |
| **模型开发** | 🟠 P1 | 1个 | 汇总逻辑实现 | 🟡中 | 3小时 |
| **接口开发** | 🟠 P1 | 1个 | API接口完成 | 🟡中 | 2小时 |
| **测试验证** | 🟡 P2 | 1个 | 单元测试通过 | 🟢低 | 2小时 |

### :wrapped_gift: 预期产出
- ✅ 完整的分层汇总数据库表设计
- ✅ 支持四级汇总统计的数据模型
- ✅ 完整的RESTful API接口
- ✅ 可编辑的计划采购和采购未交字段
- ✅ 全面覆盖的单元测试

## 📋 **上下文施工蓝图预留空间**

### 当前数据结构图
```
lingxing_fba_storage_detail (明细表)
├── 基础信息: asin, sku, sid, site_code
├── 库存数据: afn_fulfillable_quantity, reserved_fc_transfers
├── 成本价格: afn_fulfillable_quantity_price
└── 同步日期: sync_date

listing_data (配置表)
├── 产品阶段: product_stage
├── 备货定位: stock_positioning  
└── 产品定位: product_positioning
```

### 目标数据结构图
```
fba_storage_summary (汇总表)
├── 层级维度: level_type, asin, sku, site_code, sid
├── 配置信息: product_stage, stock_positioning, product_positioning
├── 库存核心: fba_sellable_qty, fba_pending_transfer_qty, inventory_plus_inbound_qty
├── 可编辑字段: planned_purchase_qty, purchase_days, purchase_pending_qty
├── 销量统计: daily_avg_sales_qty, sales_7days_qty, sales_30days_qty
└── 汇总信息: shop_count, site_count, sku_count, shop_list
```

### 关键文件依赖图
```
sql/fba_storage_summary.sql (数据库表)
├── models/fbaStorageSummaryModel.php (数据模型)
├── controller/fbaStorageSummaryController.php (API控制器)
├── form/fbaStorageSummaryForm.php (表单验证)
└── tests/fbaStorageSummaryTest.php (单元测试)
```

## 🗄️ **数据库设计**

### 汇总层级设计
- **层级1**: 店铺级 (level_type=1) - 最细粒度，包含sid+asin+sku+site_code
- **层级2**: 站点级 (level_type=2) - 按asin+site_code汇总
- **层级3**: SKU级 (level_type=3) - 按asin+sku汇总
- **层级4**: ASIN级 (level_type=4) - 按asin汇总

### 核心字段说明
| 字段名 | 类型 | 说明 | 是否可编辑 |
|--------|------|------|------------|
| fba_sellable_qty | int | FBA可售数量 | ❌ |
| fba_pending_transfer_qty | int | FBA待调仓数量 | ❌ |
| fba_transferring_qty | int | FBA调仓中数量 | ❌ |
| fba_inbound_qty | int | FBA在途数量 | ❌ |
| inventory_plus_inbound_qty | int | 库存+在途数量 | ❌ |
| planned_purchase_qty | int | 计划采购数量 | ✅ |
| purchase_days | int | 采购天数 | ✅ |
| purchase_pending_qty | int | 采购未交数量 | ✅ |

### 汇总统计流程
1. **店铺级汇总**: 从lingxing_fba_storage_detail按 asin+sku+sid+site_code 分组统计
2. **SKU级汇总**: 从店铺级数据按 asin+sku 分组汇总
3. **站点级汇总**: 从店铺级数据按 asin+site_code 分组汇总  
4. **ASIN级汇总**: 从站点级数据按 asin 分组汇总

## 🔌 **API接口文档**

### 基础路径
```
/plugins/logistics/fbaStorageSummary/
```

### 接口列表

#### 1. 获取汇总统计列表
```http
GET /list
```

**请求参数:**
```json
{
  "page": 1,
  "page_size": 20,
  "level_type": 1,
  "asin": "B07ABC123",
  "sku": "TEST-SKU-001",
  "site_code": "US",
  "sid": 123,
  "product_stage": 1,
  "stock_positioning": 1,
  "product_positioning": 1,
  "sync_date": "2025-06-20"
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "list": [
      {
        "id": 1,
        "level_type": 1,
        "level_type_text": "店铺级",
        "asin": "B07ABC123",
        "sku": "TEST-SKU-001",
        "site_code": "US",
        "sid": 123,
        "fba_sellable_qty": 500,
        "fba_pending_transfer_qty": 50,
        "inventory_plus_inbound_qty": 600,
        "planned_purchase_qty": 100,
        "purchase_days": 30,
        "purchase_pending_qty": 50,
        "product_stage": 1,
        "product_stage_text": "成长期",
        "sync_date": "2025-06-20"
      }
    ]
  }
}
```

#### 2. 获取汇总详情
```http
GET /detail/{id}
```

#### 3. 编辑汇总记录
```http
POST /edit/{id}
```

**请求体:**
```json
{
  "planned_purchase_qty": 100,
  "purchase_days": 30,
  "purchase_pending_qty": 50
}
```

#### 4. 批量编辑
```http
POST /batchEdit
```

**请求体:**
```json
{
  "items": [
    {
      "id": 1,
      "planned_purchase_qty": 100,
      "purchase_days": 30
    },
    {
      "id": 2,
      "planned_purchase_qty": 200,
      "purchase_pending_qty": 80
    }
  ]
}
```

#### 5. 执行汇总统计
```http
POST /execute
```

**请求体:**
```json
{
  "sync_date": "2025-06-20"
}
```

#### 6. 获取字典数据
```http
GET /dictionaries
```

#### 7. 获取概览数据
```http
GET /overview?sync_date=2025-06-20
```

## 🧪 **单元测试**

### 测试覆盖范围
- ✅ 模型基础功能测试
- ✅ 表单验证测试
- ✅ 参数边界值测试
- ✅ 异常处理测试
- ✅ 数据类型转换测试
- ✅ 批量操作测试

### 运行测试
```bash
cd plugins/logistics/tests/
php fbaStorageSummaryTest.php
```

### 测试用例示例
```php
// 测试表单验证
public function testValidateListParams()
{
    $validParams = [
        'page' => 1,
        'page_size' => 20,
        'level_type' => 1,
        'asin' => 'B07ABC123'
    ];
    
    $result = $this->form->validateListParams($validParams);
    $this->assertEquals(1, $result['page']);
}

// 测试边界值
public function testBoundaryValues()
{
    $this->expectException(\Exception::class);
    $this->form->validateListParams(['page_size' => 101]);
}
```

## 🔧 **使用示例**

### 1. 执行日常汇总统计
```php
use plugins\logistics\models\fbaStorageSummaryModel;

$model = new fbaStorageSummaryModel();
$result = $model->executeFullSummary('2025-06-20');
```

### 2. 查询店铺级汇总数据
```php
$params = [
    'level_type' => 1,
    'sync_date' => '2025-06-20',
    'page' => 1,
    'page_size' => 50
];

$list = $model->getSummaryList($params);
```

### 3. 批量编辑采购计划
```php
$items = [
    ['id' => 1, 'planned_purchase_qty' => 100],
    ['id' => 2, 'planned_purchase_qty' => 200]
];

foreach ($items as $item) {
    $model->updateEditableFields($item['id'], [
        'planned_purchase_qty' => $item['planned_purchase_qty']
    ]);
}
```

## 📊 **业务价值**

### 解决的痛点
1. **数据分散**: 原始明细数据过于分散，难以快速获得汇总视图
2. **缺乏层级**: 无法按不同维度（ASIN、SKU、站点、店铺）快速汇总
3. **编辑困难**: 计划采购、采购未交等业务字段无法便捷编辑
4. **效率低下**: 每次查询都需要实时计算，响应速度慢

### 带来的价值
1. **提升效率**: 预计算汇总数据，查询响应速度提升80%
2. **灵活展示**: 支持四个层级的灵活切换查看
3. **便捷编辑**: 支持单个和批量编辑采购计划字段
4. **数据一致**: 统一的汇总逻辑确保数据一致性

## 🚀 **扩展规划**

### 近期优化
- [ ] 增加销量预测算法
- [ ] 添加库存预警功能
- [ ] 支持更多维度的自定义汇总

### 长期规划  
- [ ] 集成机器学习预测模型
- [ ] 增加可视化图表展示
- [ ] 支持导出Excel功能

## 🔍 **注意事项**

### 性能考虑
- 汇总统计建议在非业务高峰期执行
- 大数据量情况下可考虑分批处理
- 定期清理历史汇总数据

### 数据一致性
- 确保明细数据和汇总数据的同步
- 汇总失败时需要事务回滚
- 建议增加数据校验机制

### 权限控制
- 编辑功能需要适当的权限控制
- 敏感操作需要记录操作日志
- 批量操作需要额外审核机制

---

*最后更新: 2025-06-20*  
*版本: v1.0.0*
