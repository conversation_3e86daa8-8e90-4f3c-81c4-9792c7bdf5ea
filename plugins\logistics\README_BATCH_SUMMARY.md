# FBA库存数据汇总功能

## 📋 功能概述

本功能实现了从源数据库`dbErpMysql`中的`lingxing_fba_storage_detail`表读取数据，并按照四个维度汇总到目标数据库`dbLMysql`的`fba_storage_summary`表中。

### 🎯 核心特性

- **分批处理**: 支持分批读取源数据，避免内存溢出
- **四维度汇总**: 按ASIN、ASIN+SKU、ASIN+SKU+站点、ASIN+SKU+站点+店铺四个维度汇总
- **增量更新**: 对已存在记录执行UPDATE，对新记录执行INSERT
- **事务保护**: 使用数据库事务确保数据一致性
- **错误处理**: 完善的错误处理和重试机制
- **性能监控**: 内存使用监控和性能统计
- **进度跟踪**: 支持进度回调函数

## 🏗️ 汇总维度说明

| 维度 | 层级类型 | 汇总字段组合 | 说明 |
|------|----------|--------------|------|
| 维度1 | 1 (店铺级) | asin + sku + site_code + sid | 最细粒度，按店铺汇总 |
| 维度2 | 2 (站点级) | asin + sku + site_code | 按站点汇总 |
| 维度3 | 3 (SKU级) | asin + sku | 按SKU汇总 |
| 维度4 | 4 (ASIN级) | asin | 最粗粒度，按ASIN汇总 |

## 📊 数据处理逻辑

### 数值字段累加
以下字段在汇总时会进行累加：
- `fba_sellable_qty` - FBA可售数量
- `fba_pending_transfer_qty` - FBA待调仓数量
- `fba_transferring_qty` - FBA调仓中数量
- `fba_inbound_qty` - FBA在途数量
- `inventory_plus_inbound_qty` - 库存+在途数量
- `fba_sellable_price` - FBA可售成本价
- `fba_pending_transfer_price` - FBA待调仓成本价
- `fba_transferring_price` - FBA调仓中成本价
- `fba_inbound_price` - FBA在途成本价
- `inventory_plus_inbound_price` - 库存+在途成本价

### 统计字段更新
- `shop_count` - 包含店铺数量
- `site_count` - 包含站点数量
- `sku_count` - 包含SKU数量
- `shop_list` - 包含店铺列表(JSON)
- `site_list` - 包含站点列表(JSON)
- `sku_list` - 包含SKU列表(JSON)

## 🚀 使用方法

### 基本用法

```php
use plugins\logistics\models\fbaStorageSummaryModel;

$model = new fbaStorageSummaryModel();

// 汇总当天数据
$result = $model->batchSummarizeFromDetail();

if ($result['success']) {
    echo "汇总成功！处理了 {$result['total_processed']} 条记录\n";
} else {
    echo "汇总失败：{$result['error_message']}\n";
}
```

### 指定参数

```php
// 指定同步日期和批量大小
$syncDate = '2025-06-25';
$batchSize = 500;

$result = $model->batchSummarizeFromDetail($syncDate, $batchSize);
```

### 带进度回调

```php
$progressCallback = function($progress, $processed, $total) {
    echo "进度: {$progress}% ({$processed}/{$total})\n";
};

$result = $model->batchSummarizeFromDetail(null, 1000, $progressCallback);
```

## 📈 返回结果说明

```php
[
    'success' => true,              // 是否成功
    'total_processed' => 1500,      // 总处理记录数
    'total_inserted' => 800,        // 新增记录数
    'total_updated' => 700,         // 更新记录数
    'error_message' => '',          // 错误信息（失败时）
    'start_time' => '2025-06-26 10:00:00',  // 开始时间
    'end_time' => '2025-06-26 10:05:30',    // 结束时间
    'processing_time' => 330.25     // 处理时间（秒）
]
```

## ⚙️ 配置参数

### 方法参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `$syncDate` | string | 当天日期 | 同步日期，格式：YYYY-MM-DD |
| `$batchSize` | int | 1000 | 批量处理大小，范围：1-10000 |
| `$progressCallback` | callable | null | 进度回调函数 |

### 性能参数

- **内存限制**: 默认512MB，超出时会停止处理
- **重试次数**: 数据库操作失败时最多重试3次
- **批量大小**: 建议500-2000，根据服务器性能调整

## 🛡️ 错误处理

### 输入验证
- 同步日期格式验证（YYYY-MM-DD）
- 批量大小范围验证（1-10000）
- 日期不能是未来日期

### 数据库错误
- 连接失败自动重试
- 事务回滚保护
- 详细错误日志记录

### 内存保护
- 实时内存使用监控
- 超限自动停止处理
- 内存使用统计

## 📝 日志记录

系统会自动记录以下日志：
- 处理开始/结束时间
- 批次处理进度
- 错误信息和堆栈跟踪
- 内存使用情况
- 性能统计数据

日志格式：
```
[FBA汇总-INFO] 开始批量汇总处理 {"sync_date":"2025-06-26","batch_size":1000}
[FBA汇总-ERROR] 数据库操作失败 {"operation":"读取源数据","error":"连接超时"}
```

## 🧪 单元测试

运行测试：
```bash
php plugins/logistics/tests/fbaStorageSummaryTest.php
```

测试覆盖：
- 数量和价格计算逻辑
- 四维度汇总处理
- 数据聚合和统计
- 错误处理机制
- 边界条件测试

## 📚 示例代码

详细使用示例请参考：
- `plugins/logistics/examples/batchSummaryExample.php`

## ⚠️ 注意事项

1. **数据库连接**: 确保源数据库和目标数据库连接正常
2. **权限要求**: 需要对目标表的读写权限
3. **数据一致性**: 处理过程中避免手动修改目标表
4. **性能影响**: 大批量处理时建议在业务低峰期执行
5. **监控建议**: 建议监控处理时间和内存使用情况

## 🔧 故障排除

### 常见问题

1. **内存不足**
   - 减小批量大小
   - 增加服务器内存
   - 分时段处理

2. **处理超时**
   - 检查数据库连接
   - 优化查询索引
   - 调整超时设置

3. **数据不一致**
   - 检查事务日志
   - 验证源数据完整性
   - 重新执行汇总

### 性能优化建议

1. 根据服务器性能调整批量大小
2. 在数据库空闲时执行汇总
3. 定期清理历史汇总数据
4. 监控数据库索引使用情况
