# FBA库存数据汇总功能定时任务集成

## 📋 功能概述

本文档说明如何将FBA库存数据汇总功能集成到现有的定时任务系统中，实现自动化的数据拉取和汇总处理。

## 🏗️ 集成架构

```
定时任务调度器
    ↓
lingxing_fba_storage.sh (Shell脚本)
    ↓
1. 调用数据拉取接口 (synStorageFbaDetail)
    ↓
2. 调用数据汇总接口 (getFbaStorageSummary)
    ↓
3. 发送结果通知 (企微机器人)
```

## 🔧 集成组件

### 1. Shell脚本修改 (`task/shell/lingxing_fba_storage.sh`)

**主要变更**：
- 在数据拉取完成后添加汇总调用
- 增加汇总结果检查和错误处理
- 优化企微通知消息格式

**执行流程**：
```bash
# 1. 循环拉取FBA库存明细数据
while true; do
    response=$(curl -s -X POST -d "token=$token" 'http://oa.ywx.com/task/lingXingApi/synStorageFbaDetail')
    # 检查返回码，code=2时表示完成
done

# 2. 执行数据汇总
summary_response=$(curl -s -X POST -d "token=$token" 'http://oa.ywx.com/task/logistics/getFbaStorageSummary')

# 3. 发送结果通知
curl "$qwUrl" -H "Content-Type: application/json" -d "通知消息"
```

### 2. 控制器方法实现 (`task/controller/logisticsController.php`)

**新增方法**：`getFbaStorageSummary()`

**功能特性**：
- 参数验证（日期格式、批量大小）
- 进度回调和日志记录
- 事务安全和错误处理
- 统一的返回格式

**接口参数**：
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `token` | string | 必需 | 认证令牌 |
| `sync_date` | string | 当天 | 同步日期 (YYYY-MM-DD) |
| `batch_size` | int | 1000 | 批量处理大小 (1-10000) |

**返回格式**：
```json
{
    "code": 2,
    "message": "FBA库存汇总完成：处理1500条，新增800条，更新700条，耗时45.32秒",
    "data": {
        "sync_date": "2025-06-26",
        "total_processed": 1500,
        "total_inserted": 800,
        "total_updated": 700,
        "processing_time": 45.32,
        "start_time": "2025-06-26 10:00:00",
        "end_time": "2025-06-26 10:00:45"
    }
}
```

## 🚀 部署和配置

### 1. 文件部署

确保以下文件已正确部署：
- `task/controller/logisticsController.php` - 控制器文件
- `task/shell/lingxing_fba_storage.sh` - 修改后的Shell脚本
- `plugins/logistics/models/fbaStorageSummaryModel.php` - 汇总模型

### 2. 权限设置

```bash
# 给Shell脚本执行权限
chmod +x task/shell/lingxing_fba_storage.sh
chmod +x task/shell/test_fba_summary_integration.sh
```

### 3. 定时任务配置

现有的crontab配置应该已经包含FBA数据拉取任务：
```bash
# 每天执行FBA库存数据处理
0 2 * * * /path/to/oa-api/task/shell/lingxing_fba_storage.sh
```

## 🧪 测试验证

### 1. 手动测试

```bash
# 测试汇总接口
curl -X POST -d "token=01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0" \
'http://oa.ywx.com/task/logistics/getFbaStorageSummary'

# 带参数测试
curl -X POST -d "token=01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0&sync_date=2025-06-25&batch_size=500" \
'http://oa.ywx.com/task/logistics/getFbaStorageSummary'
```

### 2. 集成测试

```bash
# 运行集成测试脚本
./task/shell/test_fba_summary_integration.sh
```

测试脚本会验证：
- 基本汇总接口调用
- 带参数的接口调用
- 错误参数处理
- 完整流程模拟

### 3. 日志检查

查看相关日志文件：
```bash
# 查看PHP错误日志
tail -f /var/log/php_errors.log | grep "FBA汇总"

# 查看系统日志
tail -f /var/log/syslog | grep "FBA"
```

## 📊 监控和告警

### 1. 企微机器人通知

**成功通知格式**：
```
✅ 领星FBA库存处理完成
📊 明细同步: code=2, msg=同步完成
📈 数据汇总: code=2, msg=汇总完成：处理1500条
⏰ 完成时间: 2025-06-26 10:00:45
```

**失败通知格式**：
```
❌ 领星FBA库存处理异常
📊 明细同步: code=2, msg=同步完成
📈 数据汇总: code=-1, msg=汇总失败：数据库连接错误
⏰ 失败时间: 2025-06-26 10:00:30
```

### 2. 关键指标监控

建议监控以下指标：
- 处理记录数量
- 处理耗时
- 成功率
- 错误频率

## ⚠️ 注意事项

### 1. 执行顺序

**重要**：必须确保数据拉取完成后再执行汇总，Shell脚本已经处理了这个逻辑。

### 2. 错误处理

- 数据拉取失败时不会执行汇总
- 汇总失败不会影响下次数据拉取
- 所有错误都会记录日志并发送通知

### 3. 性能考虑

- 默认批量大小为1000条，可根据服务器性能调整
- 大数据量处理时建议在业务低峰期执行
- 监控内存使用情况，避免内存溢出

### 4. 数据一致性

- 使用数据库事务确保数据一致性
- 汇总过程中避免手动修改目标表
- 支持重复执行，不会产生重复数据

## 🔧 故障排除

### 常见问题

1. **汇总接口返回-1**
   - 检查参数格式是否正确
   - 验证数据库连接是否正常
   - 查看详细错误日志

2. **处理时间过长**
   - 减小批量处理大小
   - 检查数据库性能
   - 优化查询索引

3. **内存不足**
   - 调整PHP内存限制
   - 减小批量处理大小
   - 检查是否有内存泄漏

### 调试命令

```bash
# 检查Shell脚本语法
bash -n task/shell/lingxing_fba_storage.sh

# 手动执行Shell脚本
bash -x task/shell/lingxing_fba_storage.sh

# 测试汇总功能
php -f plugins/logistics/examples/batchSummaryExample.php
```

## 📈 性能优化建议

1. **数据库优化**
   - 确保相关表有适当的索引
   - 定期清理历史数据
   - 监控数据库性能

2. **批量大小调优**
   - 根据数据量和服务器性能调整
   - 建议范围：500-2000条

3. **定时任务优化**
   - 避开业务高峰期执行
   - 考虑分时段处理大量数据

## 📞 技术支持

如遇到问题，请提供以下信息：
- 错误日志内容
- 执行参数
- 数据量规模
- 服务器配置信息
