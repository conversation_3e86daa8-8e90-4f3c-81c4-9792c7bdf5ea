<?php
/**
 * 海外仓备货单数据同步执行脚本
 * @purpose 同步领星API海外仓备货单数据
 * @Author: System
 * @Time: 2025/06/24
 */

require_once __DIR__ . '/../../../core/fk.php';

use plugins\logistics\models\overseasInboundModel;
use core\lib\log;

class executeOverseasInbound
{
    private $model;
    
    public function __construct()
    {
        $this->model = new overseasInboundModel();
    }

    /**
     * 执行海外仓备货单数据同步
     */
    public function execute()
    {
        try {
            echo "\n=== 海外仓备货单数据同步开始 ===\n";
            echo "开始时间: " . date('Y-m-d H:i:s') . "\n";
            
            // 默认同步参数
            $defaultParams = [
                'page' => 1,
                'page_size' => 50,
                'date_type' => 'create_time',
                'is_delete' => 0,
                'create_time_from' => date('Y-m-d', strtotime('-7 days')), // 默认同步最近7天
                'create_time_to' => date('Y-m-d')
            ];
            
            // 解析命令行参数
            $params = $this->parseCommandLineArgs($defaultParams);
            
            echo "同步参数: " . json_encode($params, JSON_UNESCAPED_UNICODE) . "\n";
            
            // 调用同步接口
            $result = $this->callSyncAPI($params);
            
            if ($result['success']) {
                echo "✅ 同步成功！\n";
                echo "处理数据: {$result['processed_count']} 条\n";
                echo "总计数据: {$result['total_count']} 条\n";
                
                // 记录成功日志
                log::lingXingApi('OverseasInbound')->info(
                    "海外仓备货单数据同步成功，处理{$result['processed_count']}条，总计{$result['total_count']}条"
                );
            } else {
                echo "❌ 同步失败: {$result['error']}\n";
                
                // 记录错误日志
                log::lingXingApi('OverseasInbound')->error(
                    "海外仓备货单数据同步失败: {$result['error']}"
                );
                
                exit(1);
            }
            
        } catch (Exception $e) {
            echo "❌ 执行异常: " . $e->getMessage() . "\n";
            log::lingXingApi('OverseasInbound')->error("海外仓备货单数据同步异常: " . $e->getMessage());
            exit(1);
        } finally {
            echo "结束时间: " . date('Y-m-d H:i:s') . "\n";
            echo "=== 海外仓备货单数据同步结束 ===\n\n";
        }
    }

    /**
     * 解析命令行参数
     */
    private function parseCommandLineArgs($defaultParams)
    {
        global $argv;
        $params = $defaultParams;
        
        if (!empty($argv)) {
            for ($i = 1; $i < count($argv); $i++) {
                $arg = $argv[$i];
                
                if (strpos($arg, '--') === 0) {
                    $parts = explode('=', substr($arg, 2), 2);
                    if (count($parts) === 2) {
                        $key = $parts[0];
                        $value = $parts[1];
                        
                        // 转换特定参数类型
                        switch ($key) {
                            case 'page':
                            case 'page_size':
                            case 'status':
                            case 'sub_status':
                            case 's_wid':
                            case 'r_wid':
                            case 'is_delete':
                                $params[$key] = (int)$value;
                                break;
                            default:
                                $params[$key] = $value;
                                break;
                        }
                    }
                }
            }
        }
        
        return $params;
    }

    /**
     * 调用同步API
     */
    private function callSyncAPI($params)
    {
        try {
            // 模拟调用领星API的synInbound方法
            // 这里需要替换为实际的API调用逻辑
            
            // 设置POST数据
            $_POST = $params;
            $_POST['token'] = '01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0';
            
            // 创建API控制器实例
            $apiController = new \task\controller\lingXingApiController();
            
            // 开始输出缓冲
            ob_start();
            
            try {
                // 调用同步方法
                $apiController->synInbound();
                
                // 获取输出
                $output = ob_get_clean();
                
                // 解析返回结果
                $result = $this->parseAPIResponse($output);
                
                return [
                    'success' => true,
                    'processed_count' => $result['processed_count'] ?? 0,
                    'total_count' => $result['total_count'] ?? 0,
                    'message' => $result['message'] ?? '同步成功'
                ];
                
            } catch (Exception $e) {
                ob_end_clean();
                throw $e;
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 解析API响应
     */
    private function parseAPIResponse($output)
    {
        // 尝试从输出中解析JSON响应
        $lines = explode("\n", $output);
        
        foreach ($lines as $line) {
            $line = trim($line);
            if (!empty($line) && (strpos($line, '{') === 0 || strpos($line, '[') === 0)) {
                $json = json_decode($line, true);
                if ($json !== null) {
                    return $json;
                }
            }
        }
        
        // 如果没有找到JSON，返回默认结果
        return [
            'processed_count' => 0,
            'total_count' => 0,
            'message' => '同步完成，但无法解析返回数据'
        ];
    }

    /**
     * 显示帮助信息
     */
    public function showHelp()
    {
        echo "\n海外仓备货单数据同步脚本\n\n";
        echo "用法:\n";
        echo "  php executeOverseasInbound.php [选项]\n\n";
        echo "选项:\n";
        echo "  --page=1                   页码 (默认: 1)\n";
        echo "  --page_size=50             每页数量 (默认: 50，最大: 50)\n";
        echo "  --status=10                状态筛选 (10=待审核, 20=已驳回, 30=待配货, 40=待发货, 50=待收货, 51=已撤销, 60=已完成)\n";
        echo "  --sub_status=0             子状态筛选 (0=全部, 1=未收货, 2=部分收货)\n";
        echo "  --s_wid=1                  发货仓库ID\n";
        echo "  --r_wid=2                  收货仓库ID\n";
        echo "  --overseas_order_no=XXX    备货单号筛选\n";
        echo "  --create_time_from=YYYY-MM-DD  开始时间 (默认: 7天前)\n";
        echo "  --create_time_to=YYYY-MM-DD    结束时间 (默认: 今天)\n";
        echo "  --date_type=create_time    时间类型 (create_time, update_time, delivery_time, receive_time)\n";
        echo "  --is_delete=0              删除状态 (0=未删除, 1=已删除, 2=全部)\n";
        echo "  --help                     显示此帮助信息\n\n";
        echo "示例:\n";
        echo "  # 同步最近7天的所有备货单\n";
        echo "  php executeOverseasInbound.php\n\n";
        echo "  # 同步指定日期范围的待审核备货单\n";
        echo "  php executeOverseasInbound.php --status=10 --create_time_from=2025-06-01 --create_time_to=2025-06-30\n\n";
        echo "  # 同步指定仓库的备货单\n";
        echo "  php executeOverseasInbound.php --s_wid=1 --r_wid=2\n\n";
    }

    /**
     * 验证数据库连接
     */
    public function validateDatabase()
    {
        try {
            echo "验证数据库连接...\n";
            
            // 检查表是否存在
            $result = $this->model->getInboundList(['page' => 1, 'page_size' => 1]);
            
            echo "✅ 数据库连接正常\n";
            echo "✅ 数据表结构正常\n";
            
            return true;
            
        } catch (Exception $e) {
            echo "❌ 数据库验证失败: " . $e->getMessage() . "\n";
            echo "请检查:\n";
            echo "1. 数据库连接配置是否正确\n";
            echo "2. 是否已创建 oa_l_overseas_inbound 表\n";
            echo "3. 数据库用户是否有相应权限\n";
            
            return false;
        }
    }

    /**
     * 清理测试数据
     */
    public function cleanup()
    {
        try {
            echo "清理测试数据...\n";
            
            // 这里可以添加清理逻辑，比如删除测试备货单等
            // 注意：生产环境慎用
            
            echo "✅ 清理完成\n";
            
        } catch (Exception $e) {
            echo "❌ 清理失败: " . $e->getMessage() . "\n";
        }
    }
}

// 主执行逻辑
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $executor = new executeOverseasInbound();
    
    // 检查命令行参数
    global $argv;
    if (!empty($argv) && in_array('--help', $argv)) {
        $executor->showHelp();
        exit(0);
    }
    
    if (!empty($argv) && in_array('--validate', $argv)) {
        if ($executor->validateDatabase()) {
            echo "✅ 验证通过，可以开始同步\n";
            exit(0);
        } else {
            exit(1);
        }
    }
    
    if (!empty($argv) && in_array('--cleanup', $argv)) {
        $executor->cleanup();
        exit(0);
    }
    
    // 执行数据同步
    $executor->execute();
}
