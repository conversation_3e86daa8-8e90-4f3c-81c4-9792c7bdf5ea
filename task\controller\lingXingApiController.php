<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/12 14:09
 */

namespace task\controller;

use core\lib\db\dbErpMysql;
use core\lib\db\dbFMysql;
use core\lib\db\dbLMysql;
use core\lib\log;
use financial\common\lingXingApiBase;
use financial\form\checkoutForm;
use plugins\logistics\models\inboundShipmentModel;
use plugins\logistics\models\overseasInboundModel;
use plugins\logistics\models\transportMethodModel;
use plugins\logistics\models\warehouseStatisticModel;
use task\form\goods\goodsSysForm;
use task\form\lixingXingApiForm;

class lingXingApiController extends lingXingApiBase
{
    public function __construct()
    {
        $token = $_POST['token']??'';
        if ($token != '01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0') {
            returnSuccess($_POST,'token验证失败');
        }
    }

    //获取汇率信息
    public function getRoutingList()
    {
        $date = $_POST['date']??'';
        $method = '/erp/sc/routing/finance/currency/currencyMonth';
        if (!$date) {
            $date = date('Y-m', strtotime('-1 month'));
        }
        $queryInfo = self::bizBuild(['date'=>$date]);
        $url = $queryInfo['url'].$method.'?'.$queryInfo['str'].'&sign='.urlencode($queryInfo['sign']);
        $ret = requestLingXing($url,$queryInfo['body']);
        $data = self::errorCheck($ret);
        log::lingXingApi('Routing')->info('同步'.$date.'汇率信息'.json_encode($data['data']));
        if (count($data['data'])) {
            lixingXingApiForm::editRouting($data['data'],$date);
        }
        SetReturn(2,'更新成功');
    }
    //获取亚马逊市场列表
    public function getMarketList(){
        $method = '/erp/sc/data/seller/allMarketplace';
        $queryInfo = $this->bizBuild([]);
        $url = $queryInfo['url'].$method.'?'.$queryInfo['str'].'&sign='.urlencode($queryInfo['sign']);
        $ret = requestLingXing($url,[],'GET');
        $data = self::errorCheck($ret);
        log::lingXingApi('Routing')->info('同步获取亚马逊市场列表'.json_encode($data['data']));
        if (count($data['data'])) {
            lixingXingApiForm::editMarket($data['data']);
        }
        SetReturn(2,'更新成功');
    }
    //更新依据listing更新项目
    public function updateProjectOfListing() {
        $msg = lixingXingApiForm::synProject();
        returnSuccess('',$msg);
    }
    //产品分类同步
    public function updateCategory() {
        lixingXingApiForm::synCategory();
        returnSuccess('','更新成功');
    }
    //店铺同步
    public function updateSeller() {
        lixingXingApiForm::synSeller();
        SetReturn(2,'店铺更新成功');
    }
    //msku报告 数据同步
    public function synMskuReport() {
        dd(852);
        $date = empty($_POST['date'])?date('Y-m',strtotime('-1 month')):$_POST['date'];
        checkoutForm::addMdate($date);
        $offset = $_POST['offset']??0;
        $start_time = $date.'-01';
        $end_time = date('Y-m-d',strtotime("$date-01 +1 month -1 day"));
        $method = '/bd/profit/report/open/report/msku/list';
        self::$param = [
            'offset'=>(int)$offset,
            'total'=>$offset+500,
        ];
        $param = [
            'offset'=>$offset,
            'length'=>500,
            'startDate'=>$start_time,
            'endDate'=>$end_time,
        ];
        $queryInfo = self::bizBuild($param);
        $url = $queryInfo['url'].$method.'?'.$queryInfo['str'].'&sign='.urlencode($queryInfo['sign']);
        $ret = requestLingXing($url,$queryInfo['body']);
        $data = self::errorCheck($ret);
        log::lingXingApi('MskuReport')->info('同步'.$date.'msku报告数据'.json_encode($data['data']));
        $success_count = 0;
        if (count($data['data'])) {
            if (count($data['data']['records'])) {
                $success_count =  lixingXingApiForm::saveMskuReport($data['data']['records'],$date);
            }
        }
        log::lingXingApi('MskuReport')->info('保存成功'.$success_count.'条');
        $offset+=500;
        returnSuccess(['offset'=>$offset,'total'=>$data['data']['total']],'同步成功');
    }
    //查询费用类型列表
    public function getOtherFeeType() {
        $method = '/bd/fee/management/open/feeManagement/otherFee/type';
        $queryInfo = self::bizBuild([]);
        $url = $queryInfo['url'].$method.'?'.$queryInfo['str'].'&sign='.urlencode($queryInfo['sign']);
        $ret = requestLingXing($url,'');
        dd($ret);
        $data = self::errorCheck($ret);
        /*{"code":"500","msg":"syntax error, expect {, actual [, pos 1, line 1, column 2[]","data":{"throwable":"syntax error, expect {, actual [, pos 1, line 1, column 2[]","throwTime":"2024-06-20 09:23:10","message":null,"stackTrace":null,"metadata":{}}}*/

    }
    //商品数据同步
    public function synGoods() {
        $method = '/erp/sc/routing/data/local_inventory/productList';
        $redis = (new \core\lib\predisV())::$client;
        $redis_key = 'oa_syn_goods';
        $redis_key2 = 'oa_syn_goods_detail';
        //$redis->del($redis_key2);
        if ($redis->exists($redis_key2)) {
            $redis->del($redis_key);
            goodsSysForm::updateGoodsCategoryName();
            SetReturn(2,'同步成功');
        }
        if ($redis->get($redis_key)) {
            $r_data = json_decode($redis->get($redis_key),true);
            $redis->expire($redis_key,0.5*60*60);
            $page = ($r_data['page']) + 1;
        } else {
            $page = 1;
        }
        $offset = ($page - 1)*500;
        $param = [
            'offset'=>$offset,
            'length'=>500,
        ];
        $queryInfo = self::bizBuild($param);
        $url = $queryInfo['url'].$method.'?'.$queryInfo['str'].'&sign='.urlencode($queryInfo['sign']);
        $ret = requestLingXing($url,$queryInfo['body']);
        $data = self::errorCheck($ret);
        log::lingXingApi('goods')->info('同步商品数据'.$offset);
        $success_count = 0;
        if (count($data['data'])) {
            if (count($data['data'])) {
                $success_count = lixingXingApiForm::saveGoods($data['data']);
            }
        }
        $offset += $success_count;
        if ($offset >= $data['total']) {
            $redis->del($redis_key);
            $r_data2 = [
                'page'=>0,
                'offset'=>0,
                'total'=>0,
                'message'=>'商品详情同步：正在准备',
            ];
            $redis->set($redis_key2,json_encode($r_data2));
            goodsSysForm::updateGoodsCategoryName();
            SetReturn(2,'同步成功');
        } else {
            $r_data = [
                'page'=>$page,
                'offset'=>$offset,
                'total'=>$data['total'],
                'message'=>'商品同步：已同步'.$offset.'条，一共'.$data['total'].'条',
            ];
            $redis->set($redis_key,json_encode($r_data));
            returnSuccess([],'商品同步：已同步'.$offset.'条，一共'.$data['total'].'条');
        }
    }
    //商品详情同步多个
    public function synGoodsDetailBatch() {
        $method = '/erp/sc/routing/data/local_inventory/batchGetProductInfo';
        $redis = (new \core\lib\predisV())::$client;
        $redis_key = 'oa_syn_goods_detail';
        if ($redis->get($redis_key)) {
            $r_data = json_decode($redis->get($redis_key),true);
            $redis->expire($redis_key,0.5*60*60);
            $page = ($r_data['page']) + 1;
        } else {
            $page = 1;
        }
        $dbF = dbFMysql::getInstance();
        $goods_list = $dbF->table('goods')
            ->order('id')
            ->field('sku')
            ->pages($page,100);
        $list = $goods_list['list'];
        if (count($list)) {
            $sku_list = array_column($list,'sku');
            $param = [
                'skus'=>$sku_list,
            ];
            $queryInfo = self::bizBuild($param);
            $url = $queryInfo['url'].$method.'?'.$queryInfo['str'].'&sign='.urlencode($queryInfo['sign']);
            $ret = requestLingXing($url,$queryInfo['body']);
            $data = self::errorCheck($ret);
            log::lingXingApi('goods_detail')->info('同步商品详情批量数据page='.$page);
            $success_count = 0;
            if (count($data['data'])) {
                if (count($data['data'])) {
                    $success_count =  lixingXingApiForm::saveGoodsDetail($data['data']);
                }
            }
            log::lingXingApi('goods_detail')->info('保存成功'.$success_count.'条');
            //总页数
            $offset = ($page-1)*100 + $success_count;
            if ($offset >= $goods_list['total']) {
                $redis->del($redis_key);
                SetReturn(2,'商品详情同步：已同步'.$offset.'条，一共'.$goods_list['total'].'条');
            } else {
                $r_data = [
                    'page'=>$page,
                    'offset'=>$offset,
                    'total'=>$data['total'],
                    'message'=>'商品详情同步：已同步'.$offset.'条，一共'.$goods_list['total'].'条',
                ];
                $redis->set($redis_key,json_encode($r_data));
            }
            returnSuccess([],'商品详情同步：已同步'.$offset.'条，一共'.$goods_list['total'].'条');
        } else {
            $redis->del($redis_key);
            SetReturn(2,'同步成功');
        }
    }
    //商品详情单个
    public function synGoodsDetail() {
        $method = '/erp/sc/routing/data/local_inventory/productInfo';
        $page = $_POST['page']??1;
        $page = (int)$page;
        self::$param['page'] = $page;
        $dbF = dbFMysql::getInstance();
        if ($page <= 0) {
            returnSuccess(['page'=>0],'同步成功');
        }
        $goods_list = $dbF->table('goods')
            ->field('sku')
            ->pages($page,50);
        $list = $goods_list['list'];
        if (count($list)) {
            $sku_list = array_column($list,'sku');
            foreach ($sku_list as $sku) {
                $param = [
                    'sku'=>$sku,
                ];
                $queryInfo = self::bizBuild($param);
                $url = $queryInfo['url'].$method.'?'.$queryInfo['str'].'&sign='.urlencode($queryInfo['sign']);
                $ret = requestLingXing($url,$queryInfo['body']);
                $data = self::errorCheck($ret);
                lixingXingApiForm::saveGoodsDetailOne($data['data']);
            }
            $page++;
            returnSuccess(['page'=>$page],'同步成功');
        } else {
            returnSuccess(['page'=>0],'同步成功');
        }
    }
    //供应商同步
    public function synSupplier() {
        $offset = empty($_POST['offset'])?0:(int)$_POST['offset'];
        $method = '/erp/sc/data/local_inventory/supplier';
        $redis = (new \core\lib\predisV())::$client;
        $redis_key = 'oa_syn_supplier';
        if ($redis->get($redis_key)) {
            $r_data = json_decode($redis->get($redis_key),true);
            $redis->expire($redis_key,0.5*60*60);
            $page = ($r_data['page']) + 1;
        } else {
            $page = 1;
        }
        $offset = ($page - 1)*1000;
        $param = [
            'offset'=>$offset,
            'length'=>1000,
        ];
        $queryInfo = self::bizBuild($param);
        $url = $queryInfo['url'].$method.'?'.$queryInfo['str'].'&sign='.urlencode($queryInfo['sign']);
        $ret = requestLingXing($url,$queryInfo['body']);
        $data = self::errorCheck($ret);
        if (count($data['data'])) {
            lixingXingApiForm::saveSupplier($data['data']);
            $offset = $offset + count($data['data']);
            $total_count = $data['total'];
            //总页数
            if ($offset >= $total_count) {
                $redis->del($redis_key);
                SetReturn(2,'供应商同步：已同步'.$offset.'条，一共'.$total_count.'条');
            } else {
                $r_data = [
                    'page'=>$page,
                    'offset'=>$offset,
                    'total'=>$data['total'],
                    'message'=>'供应商同步：已同步'.$offset.'条，一共'.$total_count.'条',
                ];
                $redis->set($redis_key,json_encode($r_data));
            }
            returnSuccess([],'供应商同步：已同步'.$offset.'条，一共'.$total_count.'条');
        } else {
            $redis->del($redis_key);
            SetReturn(2,'供应商同步成功');
        }
    }
    //产品新品状态更新
    public function synGoodsNewTag() {
        $msg = lixingXingApiForm::getListingFirstOrderTime();
//        $msg = lixingXingApiForm::updateNewTag();
        SetReturn(2,$msg);
    }

    // 查询利润统计-MSKU
    public function synProfitMsku()
    {
        $method = '/bd/profit/statistics/open/msku/list';
        $param = [
            'startDate'=>'2025-06-01',
            'endDate'=>'2025-06-01',
        ];
        $queryInfo = self::bizBuild($param);
        $url = $queryInfo['url'].$method.'?'.$queryInfo['str'].'&sign='.urlencode($queryInfo['sign']);
        $ret = requestLingXing($url,$queryInfo['body']);
        if(!is_array($ret)){
            returnError('数据错误');
        }
        if($ret['code'] != 0){//curl网络错误
            $msg = !empty($ret['msg'])?$ret['msg'] : 'network error!';
            $msg = !empty($ret['error_details']) ? $ret['error_details'] : $msg;
            SetReturn('-1',$msg,self::$param);
        }
        $data = $ret['data'];
        $total = isset($data['total']) ? $data['total'] : 0;
        returnError($data);
        if (count($data['data'])) {
            $erp_db = dbErpMysql::getInstance();
            $table = 'lingxing_profit_statistics_msku_2025';
            foreach ($data['data'] as $k => $v) {}
        }

    }

    // 仓库 - 仓库列表
    public function synWarehouse()
    {
        $method = '/erp/sc/data/local_inventory/warehouse';
        $type_map = [
            1 => '本地仓', // 本地仓
            3 => '海外仓', // 海外仓
            4 => '亚马逊平台仓', // 亚马逊平台仓
            6 => 'AWD仓'// AWD仓
        ];
        $type = $_POST['type']??1; // 默认本地仓
        $redis = (new \core\lib\predisV())::$client;
        $redis_key = 'oa_syn_inbound_'.$type;
        if ($redis->get($redis_key)) {
            $r_data = json_decode($redis->get($redis_key),true);
            $redis->expire($redis_key,0.5*60*60);
            $page = ($r_data['page']) + 1;
        } else {
            $page = 1;
        }
        $offset = ($page - 1) * 1000;

        $param = [
            'offset'=>$offset,
            'length'=>1000,
            'type' => intval($type),
            'is_delete' => '1,0'
        ];
        $queryInfo = self::bizBuild($param);
        
        $url = $queryInfo['url'].$method.'?'.$queryInfo['str'].'&sign='.urlencode($queryInfo['sign']);
        $ret = requestLingXing($url,$queryInfo['body']);
        if(!is_array($ret)){
            returnError('数据错误');
        }
        if($ret['code'] != 0){//curl网络错误
            $msg = !empty($ret['msg'])?$ret['msg'] : 'network error!';
            $msg = !empty($ret['error_details']) ? $ret['error_details'] : $msg;
            SetReturn(-1,'测试领星接口异常：'.$msg,self::$param);
        }
        $total = isset($ret['total']) ? $ret['total'] : 0;
        $data = $ret['data'];
        $erp_db = dbErpMysql::getInstance();
        // 查询现有仓库
        $exist_list = $erp_db->table('lingxing_warehouse')
            ->field('id,wid')
            ->where('type = :type', ['type' => $type])
            ->list();
        $exist_wids = array_column($exist_list, 'id', 'wid');
        $table = 'lingxing_warehouse';
        $successCount = 0;
        if (count($data)) {
            foreach ($data as $v) {
                $attr = [
                    'wid',
                    'type',
                    'name',
                    'is_delete',
                    'wp_id',
                    'wp_name',
                ];
                // 过滤字段
                $v = array_intersect_key($v, array_flip($attr));
                if (isset($v['wid']) && isset($exist_wids[$v['wid']])){
                    // 更新
                    $erp_db->table($table)->where('where id = :id', ['id' => $exist_wids[$v['wid']]])->update($v);
                } else {
                    // 插入
                    $erp_db->table($table)->insert($v);
                }
                $successCount++;
            }
        }
        $offset += $successCount;
        if ($offset >= $total) {
            $redis->del($redis_key);
            SetReturn(2,$type_map[$type].'仓库同步：已同步'.$offset.'条，一共'.$total.'条');
        } else {
            $r_data = [
                'page'=>$page,
                'offset'=>$offset,
                'total'=>$total,
                'message'=>$type_map[$type].'仓库同步：已同步'.$offset.'条，一共'.$total.'条',
            ];
            $redis->set($redis_key,json_encode($r_data));
            returnSuccess([],'同步仓库：已同步'.$offset.'条，一共'.$total.'条');
        }
    }

    // 运输方式 - 运输方式列表
    public function synTransportMethod()
    {
        $method = '/basicOpen/businessConfig/transportMethod/list';

        // 构建请求参数 - 运输方式列表不需要分页参数，一次性获取全部
        $param = [];

        $queryInfo = self::bizBuild($param);
        $url = $queryInfo['url'].$method.'?'.$queryInfo['str'].'&sign='.urlencode($queryInfo['sign']);
        $ret = requestLingXing($url);

        // 检查响应
        if(!is_array($ret)){
            returnError('数据错误');
        }
        if($ret['code'] != 0){
            $msg = !empty($ret['msg'])?$ret['msg'] : 'network error!';
            $msg = !empty($ret['error_details']) ? $ret['error_details'] : $msg;
            SetReturn('-1',$msg,self::$param);
        }

        $data = $ret['data'] ?? [];
        $total = $ret['total'] ?? 0;

        log::lingXingApi('TransportMethod')->info('同步运输方式列表，返回'.count($data).'条数据');

        // 保存数据（全量同步）
        $successCount = 0;
        if (count($data)) {
            $transportMethodModel = new transportMethodModel();
            $successCount = $transportMethodModel->saveTransportMethodData($data);
            log::lingXingApi('TransportMethod')->info('保存成功'.$successCount.'条');
        }

        SetReturn(2,'同步成功，共处理'.$successCount.'条数据，总计'.$total.'条');
    }

    // 库存报表-本地仓-新报表-明细
    public function synInventoryLocalDetail()
    {
        $method = '/inventory/center/openapi/storageReport/local/detail/page';
        $start_day = $_POST['start_date']??'2025-06-01';
        $end_day = $_POST['end_date']??date('Y-m-d',strtotime('-1 day'));
        $month = date('Y-m', strtotime($start_day));
        $param = [
            'start_date'=>$start_day,
            'end_date'=>$end_day,
            'offset'=>1, // 这个字段是page的意思！！！！领星你罪大恶极
            'length'=>100,
        ];
        $queryInfo = self::bizBuild($param);
        $url = $queryInfo['url'].$method.'?'.$queryInfo['str'].'&sign='.urlencode($queryInfo['sign']);
        $ret = requestLingXing($url,$queryInfo['body']);
        if(!is_array($ret)){
            returnError('数据错误');
        }
        if($ret['code'] != 0){//curl网络错误
            $msg = !empty($ret['msg'])?$ret['msg'] : 'network error!';
            $msg = !empty($ret['error_details']) ? $ret['error_details'] : $msg;
            SetReturn('-1',$msg,self::$param);
        }
        $data = $ret['data'];
        $erp_db = dbErpMysql::getInstance();
        $ldb = dbLMysql::getInstance();
        $table = 'lingxing_inventory_storage_report_local_detail_'.date('Ym', strtotime($start_day));
        $inventory_table = 'warehouse_statistic_' . date('Ym', strtotime($start_day));
        if (count($data)) {
            // 查询涉及的仓库
            $sys_wids = array_unique(array_column($data, 'sys_wid'));
            $warehouse_list = $erp_db->table('lingxing_warehouse')
                ->whereIn('id', $sys_wids)
                ->field('id,wid,type')
                ->list();
            $warehouse_ = array_column($warehouse_list, 'type', 'wid');
            foreach ($data as $v) {
                $attr = [
                    'id',
                    'sys_wid',
                    'ware_house_name',
                    'seller_name',
                    'product_name',
                    'product_type',
                    'sku',
                    'fnsku',
                    'spu',
                    'spu_name',
                    'brand',
                    'category1',
                    'category2',
                    'category3',
                    'attribute_text',
                    'day_early_cost',
                    'day_early_count',
                    'day_end_cost',
                    'day_end_count',
                    'purchase_in_cost',
                    'purchase_in_count',
                    'purchase_return_cost',
                    'purchase_return_count',
                    'outsourcing_in_cost',
                    'outsourcing_in_count',
                    'outsourcing_out_cost',
                    'outsourcing_out_count',
                    'processing_in_cost',
                    'processing_in_count',
                    'processing_out_cost',
                    'processing_out_count',
                    'allocation_in_cost',
                    'allocation_in_count',
                    'allocation_in_transit_cost',
                    'allocation_in_transit_count',
                    'allocation_out_cost',
                    'allocation_out_count',
                    'change_of_standard_in_cost',
                    'change_of_standard_in_count',
                    'change_of_standard_out_cost',
                    'change_of_standard_out_count',
                    'fba_out_cost',
                    'fba_out_count',
                    'fbm_out_cost',
                    'fbm_out_count',
                    'wfs_out_cost',
                    'wfs_out_count',
                    'return_goods_in_cost',
                    'return_goods_in_count',
                    'remove_in_cost',
                    'remove_in_count',
                    'inventory_deficit_out_cost',
                    'inventory_deficit_out_count',
                    'inventory_surplus_in_cost',
                    'inventory_surplus_in_count',
                    'split_in_cost',
                    'split_in_count',
                    'split_out_cost',
                    'split_out_count',
                    'other_in_cost',
                    'other_in_count',
                    'other_out_cost',
                    'other_out_count',
                    'gifts_in_cost',
                    'gifts_in_count',
                    'cost_adjustment',
                    'rotation_day_cost',
                    'rotation_day_count',
                    'rotation_rate_cost',
                    'rotation_rate_count',
                    'sales_ratio_cost',
                    'sales_ratio_count',
                    'child_list',
                    'created_at',
                ];
                // 过滤多余字段
                $v = array_intersect_key($v, array_flip($attr));
                $v['start_date'] = $start_day;
                $v['end_date'] = $end_day;
                $v['report_month'] = $month;
                $v['child_list'] = json_encode($v['child_list'] ?? [], JSON_UNESCAPED_UNICODE);
                $id = $erp_db->table($table)->insert($v);

                $total_data = warehouseStatisticModel::calculateTotalCountAndCost($v);
                // 存入备货仓库统计表
                $log_data = [
                    'ori_id' => $id, //  erp
                    'from_type' => 1,
                    'sys_wid' => $v['sys_wid'],
                    'ware_house_type' => $warehouse_[$v['sys_wid']] ?? '',
                    'ware_house_name' => $v['ware_house_name'],
                    'seller_name' => $v['seller_name'],
                    'sku' => $v['sku'],
                    'total_in_cost' => $total_data['total_in_cost'] ?? 0,
                    'total_in_count' => $total_data['total_in_count'] ?? 0,
                    'total_out_cost' => $total_data['total_out_cost'] ?? 0,
                    'total_out_count' => $total_data['total_out_count'] ?? 0,
                    'product_name' => $v['product_name'],
                    'report_month' => $v['report_month'],
                    'start_date' => $start_day,
                    'end_date' => $end_day,
                ];
                // 插入备货仓库统计表
                $ldb->table($inventory_table)->insert($log_data);

            }
        }
        SetReturn(2,'同步成功');

    }

    // 库存报表-海外仓-新报表-明细
        public function synInventoryOverseasDetail()
    {
        $method = '/inventory/center/openapi/storageReport/overseas/detail/page';
        $start_day = $_POST['start_date']??'2025-06-01';
        $end_day = $_POST['end_date']??date('Y-m-d',strtotime('-1 day'));
        $month = date('Y-m', strtotime($start_day));
        $param = [
            'start_date'=>$start_day,
            'end_date'=>$end_day,
            'offset'=>1, // 这个字段是page的意思！！！！领星你罪大恶极
            'length'=>100,
        ];
        $queryInfo = self::bizBuild($param);
        $url = $queryInfo['url'].$method.'?'.$queryInfo['str'].'&sign='.urlencode($queryInfo['sign']);
        $ret = requestLingXing($url,$queryInfo['body']);
        if(!is_array($ret)){
            returnError('数据错误');
        }
        if($ret['code'] != 0){//curl网络错误
            $msg = !empty($ret['msg'])?$ret['msg'] : 'network error!';
            $msg = !empty($ret['error_details']) ? $ret['error_details'] : $msg;
            SetReturn('-1',$msg,self::$param);
        }
        $data = $ret['data'];
        $erp_db = dbErpMysql::getInstance();
        $table = 'lingxing_inventory_storage_report_overseas_detail_'.date('Ym', strtotime($start_day));
        if (count($data)) {
            foreach ($data as $v) {
                $attr = [
                    "allocation_in_count",
                    "allocation_in_cost",
                    "allocation_in_transit_count",
                    "allocation_in_transit_cost",
                    "allocation_out_count",
                    "allocation_out_cost",
                    "api_day_early_count",
                    "api_day_end_count",
                    "api_sku",
                    "attribute_text",
                    "brand",
                    "category1",
                    "category2",
                    "category3",
                    "change_of_standard_in_count",
                    "change_of_standard_in_cost",
                    "change_of_standard_out_count",
                    "change_of_standard_out_cost",
                    "child_list",
                    "cost_adjustment",
                    "day_early_cost",
                    "day_early_count",
                    "day_end_cost",
                    "day_end_count",
                    "destruction_out_count",
                    "destruction_out_cost",
                    "divergence_count",
                    "fbm_out_count",
                    "fbm_out_cost",
                    "fba_out_count",
                    "fba_out_cost",
                    "fnsku",
                    "gifts_in_count",
                    "gifts_in_cost",
                    "global_tags",
                    "inventory_deficit_out_count",
                    "inventory_deficit_out_cost",
                    "inventory_surplus_in_count",
                    "inventory_surplus_in_cost",
                    "other_in_count",
                    "other_in_cost",
                    "other_out_count",
                    "other_out_cost",
                    "outsourcing_in_count",
                    "outsourcing_in_cost",
                    "outsourcing_out_count",
                    "outsourcing_out_cost",
                    "product_name",
                    "product_type",
                    "processing_in_count",
                    "processing_in_cost",
                    "processing_out_count",
                    "processing_out_cost",
                    "purchase_in_count",
                    "purchase_in_cost",
                    "purchase_return_count",
                    "purchase_return_cost",
                    "remove_in_count",
                    "remove_in_cost",
                    "return_goods_in_count",
                    "return_goods_in_cost",
                    "rotation_day_cost",
                    "rotation_day_count",
                    "rotation_rate_cost",
                    "rotation_rate_count",
                    "sales_ratio_cost",
                    "sales_ratio_count",
                    "seller_name",
                    "shein_out_count",
                    "shein_out_cost",
                    "sku",
                    "sku_attribute",
                    "spu",
                    "spu_name",
                    "split_in_count",
                    "split_in_cost",
                    "split_out_count",
                    "split_out_cost",
                    "sys_wid",
                    "temu_out_count",
                    "temu_out_cost",
                    "vc_df_out_count",
                    "vc_df_out_cost",
                    "vc_po_out_count",
                    "vc_po_out_cost",
                    "ware_house_name",
                    "wfs_out_count",
                    "wfs_out_cost"
                ];
                // 过滤多余字段
                $v = array_intersect_key($v, array_flip($attr));
                $v['start_date'] = $start_day;
                $v['end_date'] = $end_day;
                $v['report_month'] = $month;
                $v['child_list'] = json_encode($v['child_list'] ?? [], JSON_UNESCAPED_UNICODE);
                $v['global_tags'] = json_encode($v['global_tags'] ?? [], JSON_UNESCAPED_UNICODE);
                $v['sku_attribute'] = json_encode($v['sku_attribute'] ?? [], JSON_UNESCAPED_UNICODE);
                $erp_db->table($table)->insert($v);
            }
        }
        SetReturn(2,'同步成功');

    }

    // 库存报表-FBA-新版-明细
    public function synInventoryFbaDetail()
    {
        $method = '/cost/center/openApi/fba/detail/query';
        $start_day = $_POST['start_date']??'2025-06-01';
        $end_day = $_POST['end_date']??date('Y-m-d',strtotime('-1 day'));
        $month = date('Y-m', strtotime($start_day));

        $erp_db = dbErpMysql::getInstance();
        $shop_list = $erp_db->table('lingxing_shop_list')
            ->field('id,seller_id')
            ->order('id')
            ->list();

        $shop_list = array_column($shop_list,  'seller_id');

        $param = [
            'start_date'=>$month,
            'end_date'=>$month,
            'offset'=>1, // 这个字段是page的意思！！！！领星你罪大恶极
            'length'=>100,
            'seller_id'=>$shop_list
        ];
        $queryInfo = self::bizBuild($param);
        $url = $queryInfo['url'].$method.'?'.$queryInfo['str'].'&sign='.urlencode($queryInfo['sign']);
        $ret = requestLingXing($url,$queryInfo['body']);
        if(!is_array($ret)){
            returnError('数据错误');
        }
        if($ret['code'] != 0){//curl网络错误
            $msg = !empty($ret['msg'])?$ret['msg'] : 'network error!';
            $msg = !empty($ret['error_details']) ? $ret['error_details'] : $msg;
            SetReturn('-1',$msg,self::$param);
        }
        $data = $ret['data']['row_data'];
        $table = 'lingxing_inventory_storage_report_fba_detail_'.date('Ym', strtotime($start_day));
        if (count($data)) {
            $attr = [
                "adjustments_count",
                "adjustments_logistic_amount",
                "adjustments_other_amount",
                "adjustments_total_amount",
                "asin",
                "bid",
                "brand_name",
                "child_data",
                "cid",
                "country_code",
                "customer_returns_count",
                "customer_returns_logistic_amount",
                "customer_returns_other_amount",
                "customer_returns_total_amount",
                "damaged_count",
                "damaged_logistic_amount",
                "damaged_other_amount",
                "damaged_total_amount",
                "difference_count",
                "difference_logistic_amount",
                "difference_other_amount",
                "difference_total_amount",
                "disposition",
                "disposed_count",
                "disposed_logistic_amount",
                "disposed_other_amount",
                "disposed_total_amount",
                "end_count",
                "end_logistic_amount",
                "end_on_way_count",
                "end_on_way_logistic_amount",
                "end_on_way_other_amount",
                "end_on_way_total_amount",
                "end_other_amount",
                "end_total_amount",
                "fnsku",
                "found_count",
                "found_logistic_amount",
                "found_other_amount",
                "found_total_amount",
                "inventory_turnover_rate",
                "local_name",
                "local_sku",
                "lost_count",
                "lost_logistic_amount",
                "lost_other_amount",
                "lost_total_amount",
                "mid",
                "msku",
                "other_events_count",
                "other_events_logistic_amount",
                "other_events_other_amount",
                "other_events_total_amount",
                "parent_asin",
                "parent_node",
                "product_category_name",
                "product_id",
                "receipts_count",
                "receipts_logistic_amount",
                "receipts_other_amount",
                "receipts_total_amount",
                "seller_id",
                "shipments_count",
                "shipments_logistic_amount",
                "shipments_other_amount",
                "shipments_total_amount",
                "sid",
                "start_count",
                "start_logistic_amount",
                "start_other_amount",
                "start_total_amount",
                "transferring_out_count",
                "transferring_out_logistic_amount",
                "transferring_out_other_amount",
                "transferring_out_total_amount",
                "vendor_returns_count",
                "vendor_returns_logistic_amount",
                "vendor_returns_other_amount",
                "vendor_returns_total_amount",
                "ware_house_name",
                "whse_transfers_count",
                "whse_transfers_logistic_amount",
                "whse_transfers_other_amount",
                "whse_transfers_total_amount",
                "wid"
            ];
            foreach ($data as $v) {
                // 过滤多余字段
                $v = array_intersect_key($v, array_flip($attr));
                $v['start_date'] = $start_day;
                $v['end_date'] = $end_day;
                $v['report_month'] = $month;
                $v['child_data'] = json_encode($v['child_data'] ?? [], JSON_UNESCAPED_UNICODE);
                $erp_db->table($table)->insert($v);
            }
        }
        SetReturn(2,'同步成功');
    }

    // 仓库-FBA库存明细
    public function synStorageFbaDetail()
    {
        $method = '/basicOpen/openapi/storage/fbaWarehouseDetail';
        $redis = (new \core\lib\predisV())::$client;
        $redis_key = 'oa_syn_fba_storage_detail';
        if ($redis->get($redis_key)) {
            $r_data = json_decode($redis->get($redis_key),true);
            $redis->expire($redis_key,0.5*60*60);
            $page = ($r_data['page']) + 1;
        } else {
            $page = 1;
        }
        $offset = ($page - 1) * 100;
        // 构建请求参数
        $param = [
            'offset' => $offset,
            'length' => 100,
        ];
        
        // 构建请求
        $queryInfo = self::bizBuild($param);
        $url = $queryInfo['url'].$method.'?'.$queryInfo['str'].'&sign='.urlencode($queryInfo['sign']);
        $ret = requestLingXing($url,$queryInfo['body']);
        
        // 检查响应
        if(!is_array($ret)){
            returnError('数据错误');
        }
        if($ret['code'] != 0){
            $msg = !empty($ret['msg'])?$ret['msg'] : 'network error!';
            $msg = !empty($ret['error_details']) ? $ret['error_details'] : $msg;
            SetReturn('-1',$msg,self::$param);
        }
        
        $data = $ret['data'];
        $total = $ret['total'] ?? 0;
                
        // 保存数据
        $success_count = 0;
        if (count($data)) {
            $success_count = lixingXingApiForm::saveFbaStorageDetail($data);
        }
        $offset += $success_count;
        if ($offset >= $total) {
            $redis->del($redis_key);
            SetReturn(2,'FBA库存明细同步：'.$offset.'条，一共'.$total.'条');
        } else {
            $r_data = [
                'page'=>$page,
                'offset'=>$offset,
                'total'=>$total,
                'message'=>'FBA库存明细同步：已同步'.$offset.'条，一共'.$total.'条',
            ];
            $redis->set($redis_key,json_encode($r_data));
            returnSuccess([],'FBA库存明细同步：已同步'.$offset.'条，一共'.$total.'条');
        }     
    }

    // 仓库 - 备货单
    public function synInbound()
    {
        $method = '/erp/sc/routing/owms/inbound/listInbound';
        $redis = (new \core\lib\predisV())::$client;
        $redis_key = 'oa_syn_inbound';
        if ($redis->get($redis_key)) {
            $r_data = json_decode($redis->get($redis_key),true);
            $redis->expire($redis_key,0.5*60*60);
            $page = ($r_data['page']) + 1;
        } else {
            $page = 1;
        }
        // 构建请求参数
        $param = [
            'page' => (int)$page,
            'page_size' => 50, // 最大50条
        ];

        $queryInfo = self::bizBuild($param);
        $url = $queryInfo['url'].$method.'?'.$queryInfo['str'].'&sign='.urlencode($queryInfo['sign']);
        $ret = requestLingXing($url,$queryInfo['body']);

        if(!is_array($ret)){
            returnError('数据错误');
        }
        if($ret['code'] != 0){
            $msg = !empty($ret['msg'])?$ret['msg'] : 'network error!';
            $msg = !empty($ret['error_details']) ? $ret['error_details'] : $msg;
            SetReturn('-1',$msg,self::$param);
        }

        $data = $ret['data'];
        $total = $ret['total'] ?? 0;

        log::lingXingApi('OverseasInbound')->info('同步海外仓备货单列表，返回'.count($data).'条数据');

        // 保存数据
        $successCount = 0;
        if (count($data)) {
            $overseasInboundModel = new overseasInboundModel();
            $successCount = $overseasInboundModel->saveInboundData($data);
            log::lingXingApi('OverseasInbound')->info('保存成功'.$successCount.'条');
        }
        $offset = ($page - 1)*50;
        $offset += $successCount;
        if ($offset >= $total) {
            $redis->del($redis_key);
            SetReturn(2,'同步成功，共处理'.$successCount.'条数据，总计'.$total.'条');
        } else {
            $r_data = [
                'page' => $page,
                'offset' => $offset,
                'total' => $total,
                'message' => '同步海外仓备货单：已同步'.$offset.'条，一共'.$total.'条',
            ];
            $redis->set($redis_key,json_encode($r_data));
            returnSuccess([],'同步海外仓备货单：已同步'.$offset.'条，一共'.$total.'条');
        }
    }

    // FBA - 发货单
    public function synInboundShipment()
    {
        $method = '/erp/sc/routing/storage/shipment/getInboundShipmentList';
        
        // 获取请求参数
        $offset = $_POST['offset'] ?? 0;
        $length = $_POST['length'] ?? 100;
        
        // 构建请求参数
        $param = [
            'offset' => (int)$offset,
            'length' => (int)$length,
        ];
        
        // 构建请求
        $queryInfo = self::bizBuild($param);
        $url = $queryInfo['url'].$method.'?'.$queryInfo['str'].'&sign='.urlencode($queryInfo['sign']);
        $ret = requestLingXing($url,$queryInfo['body']);
        
        // 检查响应
        if(!is_array($ret)){
            returnError('数据错误');
        }
        if($ret['code'] != 0){
            $msg = !empty($ret['msg'])?$ret['msg'] : 'network error!';
            $msg = !empty($ret['error_details']) ? $ret['error_details'] : $msg;
            SetReturn('-1',$msg,self::$param);
        }
        
        $data = $ret['data'];
        $total = $data['total'] ?? 0;
        $shipmentList = $data['list'] ?? [];
        
        log::lingXingApi('InboundShipment')->info('同步发货单列表，返回'.count($shipmentList).'条数据');
        
        // 保存数据
        $successCount = 0;
        if (count($shipmentList)) {
            $shipmentModel = new inboundShipmentModel();
            $successCount = $shipmentModel->saveShipmentList($shipmentList, date('Y-m-d'));
            log::lingXingApi('InboundShipment')->info('保存成功'.$successCount.'条');
        }
        
        SetReturn(2,'同步成功，共处理'.$successCount.'条数据，总计'.$total.'条');
    }
}
