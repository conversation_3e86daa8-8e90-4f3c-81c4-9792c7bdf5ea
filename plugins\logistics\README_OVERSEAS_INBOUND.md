# 海外仓备货单管理系统

## 概述

海外仓备货单管理系统是物流插件的一个重要组成部分，用于管理从领星API同步的海外仓备货单数据，提供完整的CRUD操作和数据查询功能。

## 功能特性

### 1. 数据同步
- 从领星API自动同步海外仓备货单数据
- 支持增量更新，避免重复插入
- 自动处理JSON格式的商品和物流信息
- 保留用户自定义的可编辑字段

### 2. 数据查询
- 多维度筛选：状态、子状态、仓库、时间范围等
- 分页查询，支持最大50条/页
- 支持模糊搜索备货单号
- 灵活的时间筛选（创建时间、发货时间、收货时间、更新时间）

### 3. 数据管理
- 更新可编辑字段：透明标、入仓编码、店铺代码、FNSKU等
- 逻辑删除功能
- 批量操作支持
- 数据导出功能

### 4. 数据验证
- 完整的参数验证机制
- 字段长度和格式验证
- 业务规则验证
- 错误信息收集和返回

## 文件结构

```
plugins/logistics/
├── sql/
│   └── overseas_inbound.sql              # 数据库表结构
├── models/
│   └── overseasInboundModel.php          # 数据模型
├── controller/
│   └── overseasInboundController.php     # 控制器
├── form/
│   └── overseasInboundForm.php           # 表单验证
├── tests/
│   └── overseasInboundModelTest.php      # 单元测试
└── README_OVERSEAS_INBOUND.md            # 本文档
```

## 数据库设计

### 主表：oa_l_overseas_inbound

| 字段名 | 类型 | 说明 | 可编辑 |
|--------|------|------|--------|
| id | int(11) | 主键ID | 否 |
| overseas_order_no | varchar(100) | 备货单号 | 否 |
| inbound_order_no | varchar(100) | 三方入库单号 | 否 |
| customer_reference_no | varchar(100) | 客户提交参考号 | 否 |
| s_wid | int(11) | 发货仓id | 否 |
| s_wname | varchar(200) | 发货仓名称 | 否 |
| r_wid | int(11) | 收货仓id | 否 |
| r_wname | varchar(200) | 收货仓名称 | 否 |
| logistics_id | int(11) | 物流方式id | 否 |
| logistics_name | varchar(200) | 物流方式名称 | 否 |
| status | tinyint(3) | 状态 | 否 |
| sub_status | tinyint(3) | 子状态 | 否 |
| transparent_label | varchar(200) | 透明标 | 是 |
| warehouse_code | varchar(100) | 入仓编码 | 是 |
| shop_code | varchar(100) | 店铺代码 | 是 |
| fnsku | varchar(200) | FNSKU | 是 |
| remaining_available | int(11) | 剩余可用 | 是 |
| shipping_remark | text | 发货备注 | 是 |
| other_remark | text | 其他备注 | 是 |
| products | text | 商品信息JSON | 否 |
| logistics | text | 物流数据JSON | 否 |
| head_logistics_list | text | 新版头程物流信息JSON | 否 |

### 状态定义

| 状态值 | 状态名称 |
|--------|----------|
| 10 | 待审核 |
| 20 | 已驳回 |
| 30 | 待配货 |
| 40 | 待发货 |
| 50 | 待收货 |
| 51 | 已撤销 |
| 60 | 已完成 |

### 子状态定义

| 子状态值 | 子状态名称 |
|----------|------------|
| 0 | 全部 |
| 1 | 未收货 |
| 2 | 部分收货 |

## API接口

### 1. 数据同步接口

**接口地址：** `task/controller/lingXingApiController.php::synInbound`

**请求参数：**
```php
$_POST = [
    'status' => '',                    // 状态筛选
    'sub_status' => '',               // 子状态筛选
    's_wid' => '',                    // 发货仓库ID
    'r_wid' => '',                    // 收货仓库ID
    'overseas_order_no' => '',        // 备货单号
    'create_time_from' => '',         // 开始时间
    'create_time_to' => '',           // 结束时间
    'page' => 1,                      // 页码
    'page_size' => 20,               // 每页数量
    'date_type' => 'create_time',    // 时间类型
    'is_delete' => 0                 // 删除状态
];
```

### 2. 查询接口

**接口地址：** `plugins/logistics/controller/overseasInboundController.php::getInboundList`

**请求参数：** 同数据同步接口

**返回格式：**
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "total": 100,
        "page": 1,
        "list": [
            {
                "id": 1,
                "overseas_order_no": "OWS202506240001",
                "status": 10,
                "status_name": "待审核",
                "s_wname": "深圳仓",
                "r_wname": "美国仓",
                "transparent_label": "A001",
                "warehouse_code": "WH001",
                "products": [...],
                "logistics": [...],
                "create_time": "2025-06-24 10:00:00"
            }
        ]
    }
}
```

### 3. 详情查询接口

**接口地址：** `plugins/logistics/controller/overseasInboundController.php::getInboundDetail`

**请求参数：**
```php
$_POST = [
    'id' => 1  // 备货单ID
];
```

### 4. 更新接口

**接口地址：** `plugins/logistics/controller/overseasInboundController.php::updateEditableFields`

**请求参数：**
```php
$_POST = [
    'id' => 1,                           // 备货单ID
    'transparent_label' => 'A001',       // 透明标
    'warehouse_code' => 'WH001',         // 入仓编码
    'shop_code' => 'SHOP001',            // 店铺代码
    'fnsku' => 'X001234567890',          // FNSKU
    'remaining_available' => 100,        // 剩余可用
    'shipping_remark' => '发货备注',      // 发货备注
    'other_remark' => '其他备注'         // 其他备注
];
```

### 5. 批量操作接口

**接口地址：** `plugins/logistics/controller/overseasInboundController.php::batchUpdate`

**请求参数：**
```php
$_POST = [
    'ids' => [1, 2, 3],                 // 备货单ID数组
    'data' => [                         // 更新数据
        'transparent_label' => 'A001',
        'shop_code' => 'SHOP001'
    ]
];
```

### 6. 导出接口

**接口地址：** `plugins/logistics/controller/overseasInboundController.php::exportInbound`

**请求参数：** 同查询接口

## 使用示例

### 1. 同步数据

```php
// 通过API调用同步最新的备货单数据
$result = requestAPI('synInbound', [
    'page' => 1,
    'page_size' => 50,
    'status' => 10,
    'create_time_from' => '2025-06-01',
    'create_time_to' => '2025-06-30'
]);
```

### 2. 查询数据

```php
use plugins\logistics\models\overseasInboundModel;

$model = new overseasInboundModel();

// 查询列表
$result = $model->getInboundList([
    'status' => 10,
    'page' => 1,
    'page_size' => 20
]);

// 查询详情
$detail = $model->getInboundDetail(1);

// 根据备货单号查询
$inbound = $model->getInboundByOrderNo('OWS202506240001');
```

### 3. 更新数据

```php
use plugins\logistics\models\overseasInboundModel;

$model = new overseasInboundModel();

$result = $model->updateEditableFields(1, [
    'transparent_label' => 'A001',
    'warehouse_code' => 'WH001',
    'shop_code' => 'SHOP001',
    'remaining_available' => 100
]);
```

### 4. 表单验证

```php
use plugins\logistics\form\overseasInboundForm;

$form = new overseasInboundForm();

// 验证查询参数
$validatedParams = $form->validateListParams($_POST);
if (!empty($form->getErrors())) {
    // 处理验证错误
    $errors = $form->getErrors();
}

// 验证可编辑字段
$validatedData = $form->validateEditableFields($_POST);
if (!empty($form->getErrors())) {
    // 处理验证错误
    $errors = $form->getErrors();
}
```

## 测试

### 运行单元测试

```bash
php plugins/logistics/tests/overseasInboundModelTest.php
```

### 测试覆盖范围

- ✅ 数据保存测试
- ✅ 数据查询测试（按ID、按备货单号、列表查询）
- ✅ 数据更新测试
- ✅ 数据删除测试
- ✅ 参数验证测试
- ✅ 状态映射测试
- ✅ 边界条件测试

## 注意事项

### 1. 数据同步
- 备货单号作为唯一键，避免重复插入
- 可编辑字段在更新时会被保留
- JSON字段会自动编码和解码

### 2. 性能优化
- 使用索引优化查询性能
- 分页查询限制最大50条
- 避免全表扫描

### 3. 数据安全
- 使用参数化查询防止SQL注入
- 严格的数据验证
- 逻辑删除保护重要数据

### 4. 扩展性
- 模块化设计，易于扩展
- 标准化的接口设计
- 完整的错误处理机制

## 错误处理

### 常见错误代码

| 错误代码 | 错误信息 | 解决方案 |
|----------|----------|----------|
| -1 | 参数验证失败 | 检查请求参数格式和值 |
| -1 | 备货单不存在 | 确认备货单ID或单号正确 |
| -1 | 更新失败 | 检查数据库连接和权限 |
| -1 | 删除失败 | 确认记录存在且有删除权限 |

### 调试建议

1. 开启日志记录，查看详细错误信息
2. 使用单元测试验证功能正确性
3. 检查数据库表结构和索引
4. 确认API接口参数格式正确

## 更新日志

### v1.0.0 (2025-06-24)
- ✨ 初版发布
- ✨ 完整的CRUD功能
- ✨ 数据同步功能
- ✨ 表单验证功能
- ✨ 单元测试覆盖
- ✨ 完整的文档说明

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交变更
4. 编写测试
5. 更新文档
6. 创建Pull Request

## 许可证

本项目采用MIT许可证，详情请参阅LICENSE文件。
