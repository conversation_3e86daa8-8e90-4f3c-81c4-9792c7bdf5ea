# FBA库存四个维度汇总统计功能

## 功能概述

本功能实现了FBA库存明细数据的四个维度汇总统计，使用PHP循环读取当天同步的数据，生成四个层级的统计数据。

## 功能特点

- **四个维度统计**：ASIN、ASIN+SKU、ASIN+SKU+站点、ASIN+SKU+站点+店铺
- **PHP循环处理**：使用PHP代码循环生成，而非数据库SQL聚合
- **数组字段支持**：非唯一键字段记录为JSON数组格式
- **事务安全**：支持事务回滚，确保数据一致性
- **完整测试**：包含单元测试验证功能正确性

## 数据维度说明

### 1. 店铺级 (level_type = 1)
- **维度键**：ASIN + SKU + 站点 + 店铺
- **特点**：最细粒度，每条记录对应一个具体的店铺SKU
- **唯一性**：asin、sku、site_code、sid组合唯一

### 2. 站点级 (level_type = 2) 
- **维度键**：ASIN + SKU + 站点
- **特点**：按站点汇总同一SKU的所有店铺数据
- **数组字段**：shop_list（店铺列表）

### 3. SKU级 (level_type = 3)
- **维度键**：ASIN + SKU
- **特点**：按SKU汇总所有站点和店铺的数据
- **数组字段**：shop_list（店铺列表）、site_list（站点列表）

### 4. ASIN级 (level_type = 4)
- **维度键**：ASIN
- **特点**：按ASIN汇总所有相关数据，包含最丰富的数组字段
- **数组字段**：
  - shop_list（店铺列表）
  - site_list（站点列表）
  - sku_list（SKU列表）
  - warehouse_list（仓库列表）
  - seller_sku_list（卖家SKU列表）
  - fnsku_list（FNSKU列表）
  - category_list（类目列表）
  - brand_list（品牌列表）

## 聚合规则

### 数量字段
累加所有明细记录的对应字段：
- fba_sellable_qty（FBA可售数量）
- fba_pending_transfer_qty（FBA待调仓数量）
- fba_transferring_qty（FBA调仓中数量）
- fba_inbound_qty（FBA在途数量）
- inventory_plus_inbound_qty（库存+在途数量）

### 价格字段
累加所有明细记录的对应字段：
- fba_sellable_price（FBA可售成本价）
- fba_pending_transfer_price（FBA待调仓成本价）
- fba_transferring_price（FBA调仓中成本价）
- fba_inbound_price（FBA在途成本价）
- inventory_plus_inbound_price（库存+在途成本价）

### 配置字段
取最大值（最严格配置）：
- product_stage（产品阶段）
- stock_positioning（备货定位）
- product_positioning（产品定位）

### 统计字段
- shop_count：唯一店铺数量
- site_count：唯一站点数量
- sku_count：唯一SKU数量

## 使用方法

### 1. 代码调用

```php
use plugins\logistics\models\fbaStorageSummaryModel;

$model = new fbaStorageSummaryModel();

// 统计当天数据
$result = $model->generateMultiDimensionSummary();

// 统计指定日期数据
$result = $model->generateMultiDimensionSummary('2025-06-23');
```

### 2. 命令行执行

```bash
# 统计当天数据
php plugins/logistics/scripts/executeMultiDimensionSummary.php

# 统计指定日期数据
php plugins/logistics/scripts/executeMultiDimensionSummary.php 2025-06-23

# 查看帮助
php plugins/logistics/scripts/executeMultiDimensionSummary.php --help
```

### 3. 单元测试

```bash
# 运行测试
php plugins/logistics/tests/fbaStorageSummaryModelTest.php
```

## 数据结构示例

### 店铺级数据示例
```json
{
  "level_type": 1,
  "asin": "B001TEST01",
  "sku": "SKU001",
  "site_code": "US",
  "sid": 1001,
  "fba_sellable_qty": 100,
  "fba_sellable_price": 1000.00,
  "shop_count": 1,
  "site_count": 1,
  "sku_count": 1
}
```

### ASIN级数据示例
```json
{
  "level_type": 4,
  "asin": "B001TEST01",
  "sku": "",
  "site_code": "",
  "sid": 0,
  "fba_sellable_qty": 180,
  "fba_sellable_price": 1800.00,
  "shop_count": 1,
  "site_count": 2,
  "sku_count": 2,
  "shop_list": "[1001]",
  "site_list": "[\"US\",\"EU\"]",
  "sku_list": "[\"SKU001\",\"SKU002\"]",
  "warehouse_list": "[\"测试仓库1\",\"测试仓库2\"]"
}
```

## 性能特点

- **内存高效**：采用流式处理，逐条处理明细数据
- **批量插入**：使用批量插入提高写入性能
- **事务保护**：出现异常时自动回滚，保证数据一致性
- **去重处理**：数组字段自动去重，避免重复数据

## 注意事项

1. **数据前置条件**：需要确保明细表 `lingxing_fba_storage_detail` 有当日同步数据
2. **幂等性**：重复执行会先清除当日数据再重新统计，保证结果一致性
3. **JSON字段**：数组字段存储为JSON格式，可使用 `json_decode()` 解析
4. **站点映射**：share_type字段自动映射为站点代码（1→US, 2→EU）
5. **配置依赖**：产品配置信息来自 `oa_erp_listing_data` 表

## 错误处理

- 数据库连接异常：自动回滚事务
- 数据解析错误：记录错误日志并抛出异常
- 内存不足：采用分批处理机制
- 重复执行：自动清理旧数据避免重复

## 扩展性

该功能设计时考虑了扩展性：
- 易于添加新的维度层级
- 支持新增聚合字段
- 可配置的数组字段列表
- 灵活的过滤条件支持

## 维护建议

1. **定期清理**：建议定期清理过期的统计数据
2. **性能监控**：监控统计执行时间和内存使用
3. **数据校验**：定期验证统计结果的准确性
4. **索引优化**：根据查询需求优化数据库索引
