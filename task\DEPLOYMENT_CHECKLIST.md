# FBA库存汇总功能部署检查清单

## 📋 部署前检查

### 1. 文件完整性检查
- [ ] `task/controller/logisticsController.php` - 控制器文件已更新
- [ ] `task/shell/lingxing_fba_storage.sh` - Shell脚本已修改
- [ ] `plugins/logistics/models/fbaStorageSummaryModel.php` - 汇总模型已实现
- [ ] `task/shell/test_fba_summary_integration.sh` - 集成测试脚本已创建
- [ ] `task/README_FBA_INTEGRATION.md` - 集成文档已创建

### 2. 数据库检查
- [ ] 源数据库 `dbErpMysql` 连接正常
- [ ] 目标数据库 `dbLMysql` 连接正常
- [ ] 表 `lingxing_fba_storage_detail` 存在且有数据
- [ ] 表 `fba_storage_summary` 存在且结构正确
- [ ] 相关索引已创建

### 3. 权限检查
- [ ] Shell脚本有执行权限
- [ ] PHP进程有数据库读写权限
- [ ] 日志目录有写入权限

## 🚀 部署步骤

### 步骤1: 备份现有文件
```bash
# 备份原始文件
cp task/controller/logisticsController.php task/controller/logisticsController.php.bak
cp task/shell/lingxing_fba_storage.sh task/shell/lingxing_fba_storage.sh.bak
```

### 步骤2: 部署新文件
```bash
# 设置执行权限
chmod +x task/shell/lingxing_fba_storage.sh
chmod +x task/shell/test_fba_summary_integration.sh
```

### 步骤3: 语法检查
```bash
# 检查PHP语法
php -l task/controller/logisticsController.php
php -l plugins/logistics/models/fbaStorageSummaryModel.php

# 检查Shell脚本语法
bash -n task/shell/lingxing_fba_storage.sh
bash -n task/shell/test_fba_summary_integration.sh
```

### 步骤4: 功能测试
```bash
# 运行集成测试
./task/shell/test_fba_summary_integration.sh

# 手动测试控制器
php task/test_integration.php
```

## 🧪 测试验证

### 1. 单元测试
- [ ] 汇总模型单元测试通过
- [ ] 数据计算逻辑正确
- [ ] 错误处理机制有效

### 2. 集成测试
- [ ] 控制器接口调用成功
- [ ] Shell脚本执行正常
- [ ] 企微通知发送成功

### 3. 端到端测试
- [ ] 完整定时任务流程测试
- [ ] 数据一致性验证
- [ ] 性能指标检查

## 📊 监控配置

### 1. 日志监控
```bash
# 添加日志监控
tail -f /var/log/php_errors.log | grep "FBA汇总"
```

### 2. 性能监控
- [ ] 处理时间监控
- [ ] 内存使用监控
- [ ] 数据库性能监控

### 3. 告警配置
- [ ] 企微机器人配置正确
- [ ] 错误告警规则设置
- [ ] 性能告警阈值配置

## 🔧 配置参数

### 1. 默认参数
```php
// 控制器默认参数
$syncDate = $_POST['sync_date'] ?? date('Y-m-d');
$batchSize = $_POST['batch_size'] ?? 1000;
```

### 2. 性能参数
- 批量大小: 1000 (可调整为500-2000)
- 内存限制: 512MB
- 超时时间: 300秒

### 3. 企微机器人
- URL: `https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=03a8032d-65c0-4390-a588-9a6b2cf11336`
- 消息格式: JSON

## ⚠️ 风险评估

### 高风险项
- [ ] 数据库连接失败
- [ ] 大数据量处理超时
- [ ] 内存溢出

### 中风险项
- [ ] 网络连接不稳定
- [ ] 磁盘空间不足
- [ ] 并发执行冲突

### 低风险项
- [ ] 日志文件过大
- [ ] 企微通知失败
- [ ] 参数格式错误

## 🔄 回滚方案

### 紧急回滚
```bash
# 恢复原始文件
cp task/controller/logisticsController.php.bak task/controller/logisticsController.php
cp task/shell/lingxing_fba_storage.sh.bak task/shell/lingxing_fba_storage.sh

# 重启相关服务
systemctl restart php-fpm
systemctl restart nginx
```

### 数据回滚
```sql
-- 如需回滚汇总数据
UPDATE fba_storage_summary SET is_deleted = 1 WHERE sync_date = '2025-06-26';
```

## 📞 应急联系

### 技术负责人
- 开发人员: [联系方式]
- 运维人员: [联系方式]
- 数据库管理员: [联系方式]

### 故障处理流程
1. 立即停止定时任务
2. 检查错误日志
3. 评估数据影响
4. 执行回滚方案
5. 通知相关人员

## ✅ 部署完成确认

### 最终检查
- [ ] 所有测试通过
- [ ] 监控配置完成
- [ ] 文档更新完成
- [ ] 团队培训完成

### 签字确认
- 开发负责人: _________________ 日期: _________
- 测试负责人: _________________ 日期: _________
- 运维负责人: _________________ 日期: _________
- 项目经理: _________________ 日期: _________

---

**部署完成时间**: _______________
**下次检查时间**: _______________
