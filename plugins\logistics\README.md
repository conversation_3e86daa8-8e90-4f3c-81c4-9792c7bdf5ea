# 仓库统计模块 - collectWarehouseData功能

## 📋 功能概述

本模块实现了从领星API拉取三种仓库数据（本地仓、海外仓、FBA仓），并将数据收集存储到`oa_l_warehouse_statistic_`统计表中，支持按仓库（sys_wid）聚合显示的完整功能。

## 🏗️ 架构设计

```mermaid
graph TD
    A[开始collectWarehouseData] --> B[调用领星API拉取数据]
    B --> C{数据类型判断}
    C --> D[本地仓数据处理]
    C --> E[海外仓数据处理] 
    C --> F[FBA仓数据处理]
    D --> G[数据转换和计算]
    E --> G
    F --> G
    G --> H[写入oa_l_warehouse_statistic_表]
    H --> I[getStatisticList按sys_wid聚合]
    I --> J[返回统计结果]
```

## 📁 文件结构

```
plugins/logistics/
├── controller/
│   └── warehouseStatisticController.php  # 控制器：API接口
├── form/
│   └── warehouseStatisticForm.php        # 业务逻辑：数据收集和处理
├── models/
│   └── warehouseStatisticModel.php       # 数据模型：数据库操作和聚合查询
├── tests/
│   └── warehouseStatisticTest.php        # 功能测试脚本
└── README.md                             # 使用说明文档
```

## 🚀 核心功能

### 1. 数据收集 (collectWarehouseData)

**数据源表：**
- 本地仓：`lingxing_inventory_storage_report_local_detail_{YYYYMM}`
- 海外仓：`lingxing_inventory_storage_report_overseas_detail_{YYYYMM}`
- FBA仓：`lingxing_inventory_storage_report_fba_detail_{YYYYMM}`

**目标表：**
- 统计表：`oa_l_warehouse_statistic_{YYYY}`

**数据字段映射：**

| 字段类型 | 本地仓/海外仓字段 | FBA仓字段 | 统计表字段 |
|---------|------------------|----------|-----------|
| 期初数量 | `day_early_count` | `start_count` | `total_day_start_count` |
| 期初成本 | `day_early_cost` | `start_total_amount` | `total_day_start_cost` |
| 期末数量 | `day_end_count` | `end_count` | `day_end_count` |
| 期末成本 | `day_end_cost` | `end_total_amount` | `day_end_cost` |
| 在途数量 | `allocation_in_transit_count` | `end_on_way_count` | `allocation_in_transit_count` |
| 在途成本 | `allocation_in_transit_cost` | `end_on_way_total_amount` | `allocation_in_transit_cost` |

### 2. 数据聚合 (getStatisticList)

**聚合规则：**
- 按 `sys_wid`（系统仓库ID）分组
- 对数值字段进行 `SUM` 聚合
- 对文本字段使用 `GROUP_CONCAT` 合并
- 支持多种筛选条件和排序

**聚合字段：**
- 期初/期末数量和成本的汇总
- 增减项数量和成本的汇总
- SKU数量统计
- 产品名称、SKU等信息的合并显示

## 🔧 API接口

### 1. 执行数据收集
```http
POST /plugins/logistics/warehouseStatistic/collectWarehouseData
Content-Type: application/x-www-form-urlencoded

year_month=2025-06
```

**响应示例：**
```json
{
    "code": 0,
    "msg": "仓库数据收集成功",
    "data": {
        "year_month": "2025-06",
        "collect_time": "2025-06-19 10:00:00",
        "message": "统计成功"
    }
}
```

### 2. 获取聚合统计列表
```http
POST /plugins/logistics/warehouseStatistic/getStatisticList
Content-Type: application/x-www-form-urlencoded

year_month=2025-06&page=1&page_size=20&warehouse_type=1&sort_field=total_day_end_cost&sort_order=desc
```

**响应示例：**
```json
{
    "code": 0,
    "msg": "获取成功",
    "data": {
        "list": [
            {
                "id": 1,
                "sys_wid": 123,
                "ware_house_name": "深圳本地仓",
                "warehouse_type": 1,
                "sku_count": 15,
                "total_day_start_count": 1000.00,
                "total_day_start_cost": 50000.00,
                "total_day_end_count": 950.00,
                "total_day_end_cost": 47500.00,
                "msku": "SKU001, SKU002, SKU003... (15个)",
                "warehouse_summary": "深圳本地仓 (15个SKU)"
            }
        ],
        "total": 50,
        "page": 1,
        "page_size": 20
    }
}
```

### 3. 导出统计数据
```http
POST /plugins/logistics/warehouseStatistic/exportStatistic
Content-Type: application/x-www-form-urlencoded

year_month=2025-06&search_type=sku&search_value=TEST
```

## 📊 数据处理逻辑

### 1. 增减项计算

**本地仓/海外仓增加项：**
- 采购入库、调拨入库、委外加工入库
- 加工入库、库存盘盈入库、其他入库
- 移除入库、规格变更入库

**本地仓/海外仓减少项：**
- 采购退回、调拨出库、委外加工出库
- 加工出库、FBA出库、FBM出库
- 库存盘亏出库、其他出库、规格变更出库

**FBA仓增加项：**
- 货件补货(`receipts_`)、买家退货(`customer_returns_`)
- 已找到(`found_`)、调整(`adjustments_`)、其他事件(`other_events_`)

**FBA仓减少项：**
- 订单发货(`shipments_`)、库存移除(`vendor_returns_`)
- 已残损(`damaged_`)、丢失(`lost_`)、弃置(`disposed_`)、移仓转移(`whse_transfers_`)

### 2. 仓库类型标识

| 仓库类型 | warehouse_type | from_type | 说明 |
|---------|---------------|-----------|------|
| 本地仓 | 1 | 1 | 国内仓库 |
| 海外仓 | 3 | 3 | 海外第三方仓库 |
| FBA仓 | 4 | 4 | 亚马逊平台仓库 |

## 🧪 测试验证

运行测试脚本验证功能：

```bash
php plugins/logistics/tests/warehouseStatisticTest.php
```

**测试项目：**
- ✅ 数据表创建
- ✅ 数据收集功能
- ✅ 聚合查询功能  
- ✅ 控制器接口

## 🔍 筛选和排序

**支持的筛选条件：**
- `warehouse_type`: 仓库类型筛选
- `product_name`: 产品名称模糊搜索
- `sku`: SKU模糊搜索
- `warehouse_name`: 仓库名称模糊搜索

**支持的排序字段：**

**SUM聚合字段（支持所有）：**
- `total_day_start_count`: 期初总数量(含移仓+在途)
- `total_day_start_cost`: 期初总成本(含移仓+在途)
- `day_start_count_with_transferring`: 期初库存数量(含移仓)
- `day_start_cost_with_transferring`: 期初库存成本(含移仓)
- `day_start_count`: 期初库存-数量
- `day_start_cost`: 期初库存-成本
- `day_start_count_transferring`: 期初移仓在途-数量
- `day_start_cost_transferring`: 期初移仓在途-总成本
- `day_start_count_in_transit`: 期初在途-数量
- `day_start_cost_in_transit`: 期初在途-成本
- `total_in_count`: 数量增加项
- `total_out_count`: 数量减少项
- `total_count`: 数量增减项（合计）
- `total_in_cost`: 成本增加项
- `total_out_cost`: 成本减少项
- `total_cost`: 成本增减项（合计）
- `total_day_end_count`: 期末总数量(含移仓+在途)
- `total_day_end_cost`: 期末总成本(含移仓+在途)（默认）
- `day_end_count`: 期末库存-数量
- `day_end_cost`: 期末库存-成本
- `allocation_in_transit_count`: 期末在途-数量
- `allocation_in_transit_cost`: 期末在途-成本
- `transferring_out_count`: 移仓在途-数量
- `transferring_out_total_amount`: 移仓在途-总成本
- `sku_count`: SKU数量

**其他字段：**
- `sys_wid`: 仓库ID
- `ware_house_name`: 仓库名称
- `warehouse_type`: 仓库类型
- `m_date`: 月份

## ⚠️ 注意事项

1. **数据表按年分表**：统计表按年份创建，如`oa_l_warehouse_statistic_2025`

2. **领星数据表依赖**：需要确保领星数据已通过API同步到对应的月度表

3. **内存使用**：大数据量时注意分页处理，避免内存溢出

4. **异常处理**：数据收集失败不会中断整个流程，会记录错误日志继续处理其他数据

5. **权限控制**：确保数据库用户有创建表和读写权限

## 🚀 部署说明

1. **确保依赖**：核心框架和数据库连接正常
2. **数据库权限**：确保有建表和读写权限
3. **领星数据**：确保领星数据表存在且有数据
4. **定时任务**：可配置定时任务自动执行数据收集

## 📈 性能优化

1. **索引优化**：在`sys_wid`和`m_date`字段上建立索引
2. **分页查询**：大数据量时使用分页避免内存问题
3. **缓存机制**：可考虑对聚合结果进行缓存
4. **批量处理**：数据插入使用批量操作提高效率

---

**作者**: warehouseStatistic  
**完成时间**: 2025-06-19  
**版本**: v1.0
