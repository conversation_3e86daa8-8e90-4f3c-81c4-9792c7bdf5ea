<?php
/**
 * FBA库存汇总统计控制器
 * @purpose 提供FBA库存汇总统计的API接口
 * @Author: System
 * @Time: 2025/06/20
 */

namespace plugins\logistics\controller;

use core\common\return_response;
use core\lib\log;
use plugins\logistics\models\fbaStorageSummaryModel;
use plugins\logistics\form\fbaStorageSummaryForm;

class fbaStorageSummaryController extends baseController
{
    private $model;
    private $form;

    public function __construct()
    {
        $this->model = new fbaStorageSummaryModel();
        $this->form = new fbaStorageSummaryForm();
    }

    /**
     * 获取FBA库存汇总统计列表
     * GET /plugins/logistics/fbaStorageSummary/list
     */
    public function getList()
    {
        try {
            // 表单验证
            $params = $this->form->validateListParams($_GET);
            
            // 获取数据
            $result = $this->model->getSummaryList($params);
            
            // 格式化数据
            if (!empty($result['list'])) {
                $levelTypeMap = $this->model->getLevelTypeMap();
                $productStageMap = $this->model->getProductStageMap();
                $stockPositioningMap = $this->model->getStockPositioningMap();
                $productPositioningMap = $this->model->getProductPositioningMap();
                
                foreach ($result['list'] as &$item) {
                    $item['level_type_text'] = $levelTypeMap[$item['level_type']] ?? '';
                    $item['product_stage_text'] = $productStageMap[$item['product_stage']] ?? '';
                    $item['stock_positioning_text'] = $stockPositioningMap[$item['stock_positioning']] ?? '';
                    $item['product_positioning_text'] = $productPositioningMap[$item['product_positioning']] ?? '';
                    
                    // 解析JSON字段
                    if (!empty($item['shop_list'])) {
                        $item['shop_list'] = json_decode($item['shop_list'], true) ?: [];
                    }
                    if (!empty($item['site_list'])) {
                        $item['site_list'] = json_decode($item['site_list'], true) ?: [];
                    }
                    if (!empty($item['sku_list'])) {
                        $item['sku_list'] = json_decode($item['sku_list'], true) ?: [];
                    }
                    if (!empty($item['sales_10days_detail'])) {
                        $item['sales_10days_detail'] = json_decode($item['sales_10days_detail'], true) ?: [];
                    }
                }
            }
            
            returnSuccess($result);
            
        } catch (\Exception $e) {
            returnError('获取FBA库存汇总统计列表失败'.$e->getMessage());
        }
    }

    /**
     * 获取FBA库存汇总统计详情
     * GET /plugins/logistics/fbaStorageSummary/detail/{id}
     */
    public function getDetail($id)
    {
        try {
            if (empty($id) || !is_numeric($id)) {
                returnError('ID参数无效');
            }
            
            $detail = $this->model->getSummaryDetail($id);
            if (!$detail) {
                returnError('记录不存在');
            }
            
            // 格式化数据
            $levelTypeMap = $this->model->getLevelTypeMap();
            $productStageMap = $this->model->getProductStageMap();
            $stockPositioningMap = $this->model->getStockPositioningMap();
            $productPositioningMap = $this->model->getProductPositioningMap();
            
            $detail['level_type_text'] = $levelTypeMap[$detail['level_type']] ?? '';
            $detail['product_stage_text'] = $productStageMap[$detail['product_stage']] ?? '';
            $detail['stock_positioning_text'] = $stockPositioningMap[$detail['stock_positioning']] ?? '';
            $detail['product_positioning_text'] = $productPositioningMap[$detail['product_positioning']] ?? '';
            
            // 解析JSON字段
            if (!empty($detail['shop_list'])) {
                $detail['shop_list'] = json_decode($detail['shop_list'], true) ?: [];
            }
            if (!empty($detail['site_list'])) {
                $detail['site_list'] = json_decode($detail['site_list'], true) ?: [];
            }
            if (!empty($detail['sku_list'])) {
                $detail['sku_list'] = json_decode($detail['sku_list'], true) ?: [];
            }
            if (!empty($detail['sales_10days_detail'])) {
                $detail['sales_10days_detail'] = json_decode($detail['sales_10days_detail'], true) ?: [];
            }
            
            returnSuccess($detail);
            
        } catch (\Exception $e) {
            returnError('获取FBA库存汇总详情失败');
        }
    }

    /**
     * 编辑FBA库存汇总统计可编辑字段
     * POST /plugins/logistics/fbaStorageSummary/edit/{id}
     */
    public function editSummary($id)
    {
        try {
            if (empty($id) || !is_numeric($id)) {
                returnError('ID参数无效');
            }
            
            // 获取POST数据
            $postData = json_decode(file_get_contents('php://input'), true);
            if (empty($postData)) {
                returnError('请求数据为空');
            }
            
            // 表单验证
            $data = $this->form->validateEditParams($postData);
            
            // 检查记录是否存在
            $existRecord = $this->model->getSummaryDetail($id);
            if (!$existRecord) {
                returnError('记录不存在');
            }
            
            // 执行更新
            $affectedRows = $this->model->updateEditableFields($id, $data);
            
            if ($affectedRows > 0) {
                returnSuccess([], '编辑成功');
            } else {
                returnError('编辑失败');
            }
            
        } catch (\Exception $e) {
            returnError('编辑失败');
        }
    }

    /**
     * 执行FBA库存汇总统计
     * POST /plugins/logistics/fbaStorageSummary/execute
     */
    public function executeSummary()
    {
        try {
            // 获取POST数据
            $postData = json_decode(file_get_contents('php://input'), true);
            $syncDate = $postData['sync_date'] ?? date('Y-m-d');
            
            // 验证日期格式
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $syncDate)) {
                returnError('日期格式无效，请使用 Y-m-d 格式');
            }
            
            // 执行汇总统计
            $result = $this->model->executeFullSummary($syncDate);
            
            if ($result) {
                returnSuccess([], '汇总统计执行成功');
            } else {
                returnError('汇总统计执行失败');
            }
            
        } catch (\Exception $e) {
            returnError('汇总统计执行失败');
        }
    }

    /**
     * 获取字典数据
     * GET /plugins/logistics/fbaStorageSummary/dictionaries
     */
    public function getDictionaries()
    {
        try {
            $dictionaries = [
                'level_type' => $this->model->getLevelTypeMap(),
                'product_stage' => $this->model->getProductStageMap(),
                'stock_positioning' => $this->model->getStockPositioningMap(),
                'product_positioning' => $this->model->getProductPositioningMap()
            ];
            
            returnSuccess($dictionaries);
            
        } catch (\Exception $e) {
            returnError('获取字典数据失败');
        }
    }

    /**
     * 获取汇总统计概览
     * GET /plugins/logistics/fbaStorageSummary/overview
     */
    public function getOverview()
    {
        try {
            $syncDate = $_GET['sync_date'] ?? date('Y-m-d');
            
            // 验证日期格式
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $syncDate)) {
                returnError('日期格式无效，请使用 Y-m-d 格式');
            }
            
            // 获取各层级统计数据
            $overview = [];
            $levelTypes = [1, 2, 3, 4];
            $levelTypeMap = $this->model->getLevelTypeMap();
            
            foreach ($levelTypes as $levelType) {
                $params = [
                    'level_type' => $levelType,
                    'sync_date' => $syncDate,
                    'page' => 1,
                    'page_size' => 1
                ];
                
                $result = $this->model->getSummaryList($params);
                $overview[$levelType] = [
                    'level_name' => $levelTypeMap[$levelType],
                    'total_count' => $result['total'],
                    'total_fba_sellable_qty' => 0,
                    'total_inventory_plus_inbound_qty' => 0,
                    'total_planned_purchase_qty' => 0,
                    'total_purchase_pending_qty' => 0
                ];
                
                // 获取汇总数据
                if ($result['total'] > 0) {
                    $summaryParams = array_merge($params, ['page_size' => $result['total']]);
                    $allData = $this->model->getSummaryList($summaryParams);
                    
                    foreach ($allData['list'] as $item) {
                        $overview[$levelType]['total_fba_sellable_qty'] += $item['fba_sellable_qty'];
                        $overview[$levelType]['total_inventory_plus_inbound_qty'] += $item['inventory_plus_inbound_qty'];
                        $overview[$levelType]['total_planned_purchase_qty'] += $item['planned_purchase_qty'];
                        $overview[$levelType]['total_purchase_pending_qty'] += $item['purchase_pending_qty'];
                    }
                }
            }

            
            
            returnSuccess([
                'sync_date' => $syncDate,
                'overview' => $overview
            ]);
            
        } catch (\Exception $e) {
            returnError('获取汇总概览失败');
        }
    }

    /**
     * 批量编辑FBA库存汇总统计
     * POST /plugins/logistics/fbaStorageSummary/batchEdit
     */
    public function batchEdit()
    {
        try {
            // 获取POST数据
            $postData = json_decode(file_get_contents('php://input'), true);
            if (empty($postData['items']) || !is_array($postData['items'])) {
                returnError('批量编辑数据为空或格式错误');
            }
            
            $successCount = 0;
            $failCount = 0;
            $errors = [];
            
            foreach ($postData['items'] as $item) {
                try {
                    if (empty($item['id']) || !is_numeric($item['id'])) {
                        $failCount++;
                        $errors[] = "ID {$item['id']} 无效";
                        continue;
                    }
                    
                    // 表单验证
                    $data = $this->form->validateEditParams($item);
                    
                    // 执行更新
                    $affectedRows = $this->model->updateEditableFields($item['id'], $data);
                    
                    if ($affectedRows > 0) {
                        $successCount++;
                    } else {
                        $failCount++;
                        $errors[] = "ID {$item['id']} 更新失败";
                    }
                    
                } catch (\Exception $e) {
                    $failCount++;
                    $errors[] = "ID {$item['id']} 处理失败: " . $e->getMessage();
                }
            }
            
            $message = "批量编辑完成：成功 {$successCount} 条，失败 {$failCount} 条";
            if (!empty($errors)) {
                $message .= "，错误详情：" . implode('; ', array_slice($errors, 0, 5));
                if (count($errors) > 5) {
                    $message .= " 等...";
                }
            }
            
            returnSuccess([
                'message' => $message,
                'success_count' => $successCount,
                'fail_count' => $failCount,
                'errors' => $errors
            ]);
            
        } catch (\Exception $e) {
            returnError('批量编辑失败');
        }
    }
}
