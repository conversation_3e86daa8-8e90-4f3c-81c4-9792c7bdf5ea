===== 记忆唤醒指令 =====

我正在进行海外仓备货单SKU拆分项目。请立即执行以下步骤恢复记忆：

1. 读取项目记忆文件：
   view .augment/tasks/overseas_inbound_sku_split.xml

2. 读取详细指导：
   view .augment/MEMORY_RECOVERY_INSTRUCTIONS.md

3. 检查当前任务状态，确认下一步工作

项目核心：从dbErpMysql.lingxing_overseas_inbound表的products JSON字段中提取SKU信息，拆分为备货单号+SKU维度的明细数据，保存到新的19字段明细表中。

products数据示例：[{"sku":"TROI-BLACK","product_name":"特洛伊-黑色","fnsku":"","stock_num":150,"batch_record_list":[{"good_num":150}]}]

需要实现：SKU拆分逻辑 + 导入导出功能（新增导入、批量修改导入分开）

请确认理解并开始工作。
