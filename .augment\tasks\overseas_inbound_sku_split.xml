<?xml version="1.0" encoding="UTF-8"?>
<project>
    <meta>
        <name>海外仓备货单SKU拆分项目</name>
        <description>从原始备货单数据中按SKU维度提取并重组数据到新的明细表</description>
        <status>planning</status>
        <priority>high</priority>
    </meta>

    <requirements>
        <business_goal>
            将海外仓备货单数据从备货单维度拆分为备货单号+SKU维度的明细数据
        </business_goal>
        <target_table_structure>
            <field name="overseas_order_no" type="varchar(100)" comment="海外仓备货单号"/>
            <field name="sku" type="varchar(100)" comment="SKU"/>
            <field name="product_name" type="varchar(200)" comment="产品名称"/>
            <field name="shop_code" type="varchar(100)" comment="店铺代码"/>
            <field name="fnsku" type="varchar(200)" comment="FNSKU"/>
            <field name="quantity" type="int" comment="数量"/>
            <field name="box_count" type="int" comment="箱数"/>
            <field name="warehouse_code" type="varchar(100)" comment="入仓编号"/>
            <field name="transparent_label" type="varchar(200)" comment="透明标"/>
            <field name="logistics_method" type="varchar(200)" comment="物流方式"/>
            <field name="target_warehouse" type="varchar(200)" comment="发往仓库"/>
            <field name="plan_time" type="datetime" comment="计划时间"/>
            <field name="ship_time" type="datetime" comment="发货时间"/>
            <field name="shipping_status" type="varchar(50)" comment="发货在途"/>
            <field name="warehouse_arrival" type="datetime" comment="仓库到货"/>
            <field name="receive_difference" type="int" comment="收货差异"/>
            <field name="remaining_available" type="int" comment="剩余可用"/>
            <field name="shipping_remark" type="text" comment="发货备注"/>
            <field name="other_remark" type="text" comment="其他备注"/>
        </target_table_structure>
    </requirements>

    <current_system>
        <data_source>
            <database>dbErpMysql</database>
            <table>lingxing_overseas_inbound</table>
            <key_fields>
                <field name="overseas_order_no" comment="备货单号"/>
                <field name="products" comment="产品列表JSON"/>
                <field name="logistics" comment="物流信息JSON"/>
            </key_fields>
        </data_source>
        <existing_files>
            <model>plugins/logistics/models/overseasInboundModel.php</model>
            <controller>plugins/logistics/controller/overseasInboundController.php</controller>
            <sql>plugins/logistics/sql/overseas_inbound.sql</sql>
            <test>plugins/logistics/tests/overseasInboundModelTest.php</test>
            <shell>task/shell/lingxing_overseas_inbound.sh</shell>
            <api_controller>task/controller/lingXingApiController.php</api_controller>
        </existing_files>
        <data_flow>
            <step>领星API拉取原始数据</step>
            <step>保存到lingxing_overseas_inbound表</step>
            <step>需要新增SKU拆分处理</step>
            <step>保存到新的明细表</step>
        </data_flow>
    </current_system>

    <technical_architecture>
        <database_connections>
            <source>dbErpMysql - 源数据库连接类</source>
            <target>dbLMysql - 目标数据库连接类</target>
        </database_connections>
        <processing_pattern>
            <approach>分批处理模式</approach>
            <batch_size>500条每批</batch_size>
            <memory_management>批次间释放内存</memory_management>
        </processing_pattern>
        <data_extraction>
            <source_field>products JSON字段包含SKU列表</source>
            <parsing_logic>解析JSON获取每个SKU的详细信息</parsing_logic>
            <mapping_rules>将备货单级别数据映射到SKU级别</mapping_rules>
        </data_extraction>
    </technical_architecture>

    <implementation_plan>
        <phase name="analysis" status="pending">
            <task name="analyze_source_data" priority="high">
                <description>分析lingxing_overseas_inbound表中products字段的JSON结构</description>
                <deliverable>数据结构分析报告</deliverable>
            </task>
            <task name="design_target_table" priority="high" status="completed">
                <description>设计目标明细表结构</description>
                <deliverable>SQL建表语句</deliverable>
                <completed_file>plugins/logistics/sql/overseas_inbound_detail.sql</completed_file>
                <notes>已创建主表、导入临时表和处理日志表，包含完整的19个业务字段和扩展字段</notes>
            </task>
            <task name="define_mapping_rules" priority="high">
                <description>定义从备货单到SKU的字段映射规则</description>
                <deliverable>映射规则文档</deliverable>
            </task>
        </phase>

        <phase name="development" status="pending">
            <task name="create_detail_table" priority="high" status="completed">
                <description>创建海外仓备货单明细表</description>
                <file>plugins/logistics/sql/overseas_inbound_detail.sql</file>
                <notes>已完成，包含主表、临时表和日志表的完整SQL</notes>
            </task>
            <task name="implement_split_model" priority="high" status="completed">
                <description>实现SKU拆分处理模型</description>
                <file>plugins/logistics/models/overseasInboundDetailModel.php</file>
                <notes>已完成，包含完整的SKU拆分逻辑和数据处理方法</notes>
            </task>
            <task name="implement_split_logic" priority="high" status="completed">
                <description>实现数据拆分核心逻辑</description>
                <methods>
                    <method>splitOrderBySku</method>
                    <method>parseProductsJson</method>
                    <method>mapOrderToSkuDetail</method>
                    <method>batchInsertDetails</method>
                </methods>
                <notes>已完成，支持JSON解析和字段映射</notes>
            </task>
            <task name="add_controller_method" priority="medium" status="completed">
                <description>在控制器中添加拆分处理方法</description>
                <file>task/controller/logisticsController.php</file>
                <method>processOverseasInboundSplit</method>
                <notes>已完成，支持循环处理每次100条数据</notes>
            </task>
            <task name="implement_import_export" priority="medium">
                <description>实现导入导出功能</description>
                <features>
                    <feature>新增导入</feature>
                    <feature>批量修改导入</feature>
                    <feature>数据导出</feature>
                </features>
            </task>
            <task name="update_shell_script" priority="medium" status="completed">
                <description>更新Shell脚本集成拆分处理</description>
                <file>task/shell/lingxing_overseas_inbound.sh</file>
                <notes>已完成，在数据同步完成后自动调用SKU拆分接口</notes>
            </task>
        </phase>

        <phase name="testing" status="pending">
            <task name="unit_tests" priority="medium">
                <description>编写单元测试</description>
                <file>plugins/logistics/tests/overseasInboundDetailTest.php</file>
            </task>
            <task name="integration_tests" priority="medium">
                <description>集成测试</description>
                <scope>完整数据流测试</scope>
            </task>
            <task name="data_validation" priority="high">
                <description>数据一致性验证</description>
                <validation>拆分前后数据量和金额校验</validation>
            </task>
        </phase>

        <phase name="deployment" status="pending">
            <task name="production_deployment" priority="low">
                <description>生产环境部署</description>
                <checklist>数据库表创建、代码部署、定时任务更新</checklist>
            </task>
        </phase>
    </implementation_plan>

    <technical_details>
        <json_parsing>
            <products_structure>
                products字段为JSON数组，每个元素包含一个SKU的完整信息
            </products_structure>
            <sample_data>
                [{"product_id":14829,"sku":"TROI-BLACK","product_code":"","product_name":"特洛伊-黑色","fnsku":"","pic_url":"...","sid":"0","seller_arr":[],"stock_num":150,"receive_num":150,"product_valid_num":null,"remark":"","s_wid":3154,"s_wname":"深圳中转仓-香港FBA","batch_record_list":[{"seller_id":"0","wid":3154,"fnsku":"","product_id":14829,"batch_no":"2409100135-1","good_num":150,"batch_order_sn":"IB240910123","purchase_order_sns":["PO240827080"],"supplier_names":["东莞市岳雅科技有限公司"],"unit_storage_cost":"58.0000","unit_cost":"0.0000","unit_head_range_cost":"0.0000","unit_purchase_price":"58.0000","storage_good_num":0}]}]
            </sample_data>
            <key_fields_mapping>
                <field source="sku" target="sku" comment="SKU代码"/>
                <field source="product_name" target="product_name" comment="产品名称"/>
                <field source="fnsku" target="fnsku" comment="FNSKU"/>
                <field source="stock_num" target="quantity" comment="数量"/>
                <field source="batch_record_list[0].good_num" target="box_count" comment="箱数"/>
            </key_fields_mapping>
        </json_parsing>
        <data_mapping>
            <inheritance_fields>
                从备货单继承的字段：overseas_order_no, logistics_method, target_warehouse, plan_time等
            </inheritance_fields>
            <sku_specific_fields>
                SKU特有字段：sku, product_name, quantity, fnsku等
            </sku_specific_fields>
        </data_mapping>
        <processing_logic>
            <batch_processing>分批读取原始数据避免内存溢出</batch_processing>
            <transaction_safety>使用数据库事务确保数据一致性</transaction_safety>
            <error_handling>异常数据记录和跳过机制</error_handling>
        </processing_logic>
    </technical_details>

    <context_recovery>
        <key_files_to_read>
            <file>plugins/logistics/models/overseasInboundModel.php</file>
            <file>plugins/logistics/sql/overseas_inbound.sql</file>
            <file>task/controller/lingXingApiController.php</file>
            <file>task/shell/lingxing_overseas_inbound.sh</file>
            <file>core/lib/db/dbErpMysql.php</file>
            <file>core/lib/db/dbLMysql.php</file>
        </key_files_to_read>
        <sample_data_query>
            SELECT overseas_order_no, products, logistics FROM lingxing_overseas_inbound LIMIT 5
        </sample_data_query>
        <understanding_checkpoints>
            <checkpoint>理解products JSON字段结构</checkpoint>
            <checkpoint>确认SKU拆分的业务规则</checkpoint>
            <checkpoint>设计目标表结构</checkpoint>
            <checkpoint>实现拆分逻辑</checkpoint>
            <checkpoint>集成到现有流程</checkpoint>
        </understanding_checkpoints>
    </context_recovery>

    <next_actions>
        <immediate>
            <action>分析现有数据结构，特别是products字段的JSON格式</action>
            <action>设计目标明细表的完整结构</action>
            <action>确定SKU拆分的具体业务规则</action>
        </immediate>
        <subsequent>
            <action>实现SKU拆分核心逻辑</action>
            <action>创建新的模型类处理明细数据</action>
            <action>集成到现有的数据处理流程</action>
        </subsequent>
    </next_actions>
</project>
