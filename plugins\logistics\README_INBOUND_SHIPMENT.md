# FBA发货单模块文档（简化版）

## 模块概述

本模块实现了FBA发货单的数据同步、存储和查询功能，通过调用领星API获取发货单列表数据，并保存到本地数据库中进行管理。采用简化的单表设计，将array字段直接存储为TEXT格式的JSON数据。

## 功能特性

- ✅ 发货单列表数据同步
- ✅ 物流轨迹信息管理（JSON存储）
- ✅ 关联货件信息处理（JSON存储）
- ✅ 文件附件管理（JSON存储）
- ✅ 数据去重和更新
- ✅ SKU和ASIN搜索功能
- ✅ 统计分析功能
- ✅ 完整的单元测试

## 设计优势

### 简化架构
- **单表设计**: 所有数据集中在一个主表中，避免复杂的关联查询
- **JSON存储**: Array字段直接存储为JSON格式，简化数据操作
- **高性能**: 减少JOIN操作，提高查询效率
- **易维护**: 表结构简单，维护成本低

### 灵活搜索
- **内容搜索**: 支持在JSON字段中搜索SKU、ASIN等信息
- **多种筛选**: 支持按状态、时间、仓库等多维度筛选
- **统计功能**: 提供完整的数据统计分析

## 文件结构

```
plugins/logistics/
├── sql/
│   └── inbound_shipment.sql           # 数据库表结构（简化版）
├── models/
│   └── inboundShipmentModel.php       # 数据模型（简化版）
├── tests/
│   ├── inboundShipmentTest.php        # 单元测试（简化版）
│   └── apiTest.php                    # API接口测试
└── README_INBOUND_SHIPMENT.md         # 说明文档
```

## 数据库设计（简化版）

### 主表结构

**lingxing_inbound_shipment** - 发货单主表

#### 基础字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int(11) | 主键ID |
| shipment_id | int(11) | 领星系统的发货单ID |
| shipment_sn | varchar(100) | 发货单号 |
| status | tinyint(2) | 发货单状态 |
| wid | int(11) | 仓库ID |
| sync_date | date | 数据同步日期 |

#### JSON存储字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| logistics_data | text | 物流轨迹信息JSON格式 |
| relate_list_data | text | 关联货件列表JSON格式 |
| not_relate_list_data | text | 未关联货件列表JSON格式 |
| file_list_data | text | 文件列表JSON格式 |

#### 索引设计
- 主键索引: `id`
- 唯一索引: `shipment_id + sync_date`
- 普通索引: `shipment_sn`, `status`, `wid`, `sync_date`

## API接口

### synInboundShipment - 发货单同步接口

**接口路径**: `/erp/sc/routing/storage/shipment/getInboundShipmentList`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| offset | int | 是 | 偏移量 |
| length | int | 是 | 每页记录数 |
| search_value | string | 否 | 搜索值 |
| search_field | string | 否 | 搜索字段（sku/shipment_sn/shipment_id） |
| sids | string | 否 | 店铺ID列表（逗号分隔） |
| mids | string | 否 | 国家ID列表（逗号分隔） |
| wid | string | 否 | 仓库ID |
| status | int | 否 | 发货单状态 |
| time_type | int | 否 | 时间类型 |
| start_date | string | 否 | 开始日期 |
| end_date | string | 否 | 结束日期 |

**使用示例**:

```php
// POST 请求到 task/controller/lingXingApiController.php
$_POST = [
    'token' => '01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0',
    'offset' => 0,
    'length' => 20,
    'start_date' => '2025-06-01',
    'end_date' => '2025-06-23',
    'status' => 0
];

// 调用 synInboundShipment 方法
```

## 核心功能

### 1. 数据同步

```php
$shipmentModel = new inboundShipmentModel();
$successCount = $shipmentModel->saveShipmentList($shipmentList, $syncDate);
```

### 2. 查询发货单列表

```php
$params = [
    'page' => 1,
    'page_size' => 20,
    'status' => 0,
    'sync_date' => '2025-06-23'
];
$result = $shipmentModel->getShipmentList($params);
```

### 3. 查询发货单详情

```php
$detail = $shipmentModel->getShipmentDetail($shipmentId);
// 自动解码JSON字段为数组格式
```

### 4. SKU搜索

```php
$result = $shipmentModel->searchBySku('TEST-SKU-001');
// 在relate_list_data JSON字段中搜索SKU
```

### 5. ASIN搜索

```php
$result = $shipmentModel->searchByAsin('B07TEST001');
// 在relate_list_data JSON字段中搜索ASIN
```

### 6. 统计分析

```php
$statistics = $shipmentModel->getShipmentStatistics([
    'sync_date' => '2025-06-23'
]);
// 返回按状态分组的统计数据
```

## JSON数据结构

### logistics_data 格式
```json
[
    {
        "logistics_list_type": 1,
        "replace_tracking_number": "88803089941",
        "tracking_number": "8880308989",
        "tracking_no": "ta1234567",
        "transport_type": 2,
        "transport_type_name": "海运",
        "order_type_code": 4,
        "order_type_code_name": "其他",
        "shippers": "emc",
        "shippers_name": "长荣emc",
        "remark": "这里是备注字段"
    }
]
```

### relate_list_data 格式
```json
[
    {
        "id": 1039,
        "mid": 318,
        "sku": "TEST-SKU-001",
        "asin": "B07TEST001",
        "product_name": "测试产品",
        "num": 1,
        "wid": 8,
        "sid": 8,
        "sname": "测试店铺"
    }
]
```

## 状态码映射

### 发货单状态

| 状态码 | 状态名称 |
|--------|----------|
| -1 | 待配货 |
| 0 | 待发货 |
| 1 | 已发货 |
| 2 | 已完成 |
| 3 | 已作废 |

### 头程费分配方式

| 类型码 | 分配方式 |
|--------|----------|
| 0 | 按计费重 |
| 1 | 按实重 |
| 2 | 按体积重 |
| 3 | 按SKU数量 |
| 4 | 自定义 |
| 5 | 按箱子体积 |

### 拣货状态

| 状态码 | 状态名称 |
|--------|----------|
| 0 | 未拣货 |
| 1 | 已拣货 |

### 打印状态

| 状态码 | 状态名称 |
|--------|----------|
| 0 | 未打印 |
| 1 | 已打印 |

## 单元测试

运行单元测试验证功能：

```bash
cd /d/phpstudy_pro/WWW/oa-api
php plugins/logistics/tests/inboundShipmentTest.php
```

### 测试覆盖范围

- ✅ 数据库连接测试
- ✅ 数据表创建测试
- ✅ 发货单数据保存测试
- ✅ 发货单列表查询测试
- ✅ 发货单详情查询测试
- ✅ SKU搜索功能测试
- ✅ ASIN搜索功能测试
- ✅ 状态映射测试
- ✅ 统计功能测试
- ✅ 数据更新测试
- ✅ 测试数据清理

## 性能优化

### 数据存储优化

- **单表设计**: 避免复杂的JOIN操作，提高查询性能
- **JSON存储**: 直接存储复杂数据结构，减少表关联
- **合理索引**: 在关键查询字段上建立索引

### 查询优化

- **分页查询**: 支持高效的分页查询
- **条件筛选**: 在基础字段上进行筛选，然后处理JSON数据
- **缓存机制**: 可扩展添加Redis缓存

### JSON搜索优化

```sql
-- SKU搜索示例
SELECT * FROM lingxing_inbound_shipment 
WHERE is_deleted = 0 
AND relate_list_data LIKE '%"sku":"TEST-SKU-001"%';

-- ASIN搜索示例  
SELECT * FROM lingxing_inbound_shipment 
WHERE is_deleted = 0 
AND relate_list_data LIKE '%"asin":"B07TEST001"%';
```

## 扩展功能

### 高级搜索

```php
// 组合搜索条件
$params = [
    'shipment_sn' => 'SP250623',
    'status' => 0,
    'wid' => 8,
    'start_date' => '2025-06-01',
    'end_date' => '2025-06-23'
];
$result = $shipmentModel->getShipmentList($params);
```

### 数据导出

```php
// 获取所有符合条件的数据（不分页）
$allData = $shipmentModel->getShipmentList(['page_size' => 999999]);
// 处理导出逻辑
```

### 批量操作

```php
// 批量更新状态
foreach ($shipmentIds as $id) {
    // 实现批量更新逻辑
}
```

## 错误处理

### 常见错误及解决方案

1. **JSON解析失败**
   - 检查JSON数据格式
   - 验证数据编码

2. **搜索性能问题**
   - 考虑添加全文索引
   - 优化LIKE查询条件

3. **数据同步失败**
   - 检查网络连接
   - 验证API参数格式

## 安全考虑

### 数据安全

- **参数化查询**: 防止SQL注入攻击
- **JSON转义**: 正确处理特殊字符
- **访问控制**: Token验证机制

### 性能安全

- **查询限制**: 限制单次查询数据量
- **超时控制**: 设置合理的超时时间
- **资源监控**: 监控数据库性能

## 维护指南

### 日常维护

- **数据清理**: 定期清理过期数据
- **索引优化**: 根据查询模式优化索引
- **性能监控**: 监控查询性能指标

### 数据备份

- **定期备份**: 建立数据备份策略
- **增量备份**: 实现增量数据备份
- **恢复测试**: 定期测试数据恢复

## 总结

简化版FBA发货单模块采用单表设计，将复杂的关联数据以JSON格式存储，在保证功能完整性的同时，大大简化了数据库结构和查询逻辑。这种设计具有以下优势：

1. **架构简单**: 单表设计，易于理解和维护
2. **性能优异**: 避免复杂JOIN，查询效率高
3. **功能完整**: 支持所有原有功能，包括搜索和统计
4. **扩展性好**: 易于添加新功能和字段
5. **维护便利**: 减少了数据一致性问题

该设计特别适合于读多写少的场景，能够有效支撑发货单数据的查询和分析需求。

---

*文档最后更新时间: 2025-06-23*
