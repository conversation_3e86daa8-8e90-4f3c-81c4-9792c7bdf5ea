{"test_time": "2025-06-23 06:30:32", "config": {"base_url": "http://localhost/oa-api", "token": "01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0", "timeout": 30, "retry_times": 3, "debug": false, "log_requests": true, "performance_mode": true}, "test_results": [{"test_name": "参数验证测试", "passed": false, "message": "Token验证功能异常", "timestamp": "2025-06-23 06:30:30"}, {"test_name": "发货单同步接口测试", "passed": false, "message": "响应格式异常", "timestamp": "2025-06-23 06:30:30"}, {"test_name": "不同状态查询测试", "passed": false, "message": "状态查询测试完成: 0/4 个状态测试通过", "timestamp": "2025-06-23 06:30:32"}], "performance_data": [{"test_name": "发货单同步接口测试", "response_time": 6.02, "success": true, "timestamp": "2025-06-23 06:30:30"}, {"test_name": "状态-1查询", "response_time": 5.82, "success": false, "timestamp": "2025-06-23 06:30:30"}, {"test_name": "状态0查询", "response_time": 5.58, "success": false, "timestamp": "2025-06-23 06:30:30"}, {"test_name": "状态1查询", "response_time": 28.56, "success": false, "timestamp": "2025-06-23 06:30:31"}, {"test_name": "状态3查询", "response_time": 4.84, "success": false, "timestamp": "2025-06-23 06:30:31"}], "summary": {"total_tests": 3, "passed_tests": 0, "total_requests": 5, "avg_response_time": 10.16}}