<?php
/**
 * FBA库存汇总统计单元测试
 * @purpose 测试FBA库存汇总统计功能
 * @Author: System  
 * @Time: 2025/06/20
 */
define('SELF_FK', realpath('')); //当前框架所在的根目录
//define('SELF_FK', $_SERVER['DOCUMENT_ROOT']); //当前框架所在的根目录
define('CORE', SELF_FK.'/core'); //核心文件目录
define('DEBUG', true); //调试模式设置
define('LOG_PATH', SELF_FK.'/log'); //调试模式设置
define('SYS_PUBLISH',false); //系统发版中

define('ROOT', SELF_FK."/");
require_once "environment.php"; //系统环境
require_once ROOT."/vendor/autoload.php";
include  CORE.'/common/function.php';
include  CORE.'/common/return.php';
include  CORE.'/common/phperror.php';
//方法请求
include CORE.'/fk.php';
//自动加载类
spl_autoload_register('\core\fk::load');

use plugins\logistics\models\fbaStorageSummaryModel;
use plugins\logistics\controller\fbaStorageSummaryController;
use plugins\logistics\form\fbaStorageSummaryForm;

/**
 * 测试工具类
 */
class TestHelper 
{
    private static $passCount = 0;
    private static $failCount = 0;
    private static $testResults = [];

    /**
     * 断言相等
     */
    public static function assertEquals($expected, $actual, $message = '')
    {
        if ($expected === $actual) {
            self::$passCount++;
            self::logResult(true, $message ?: "断言相等通过: $expected === $actual");
        } else {
            self::$failCount++;
            self::logResult(false, $message ?: "断言相等失败: 期望 $expected, 实际 $actual");
        }
    }

    /**
     * 断言为数组
     */
    public static function assertIsArray($value, $message = '')
    {
        if (is_array($value)) {
            self::$passCount++;
            self::logResult(true, $message ?: "断言为数组通过");
        } else {
            self::$failCount++;
            self::logResult(false, $message ?: "断言为数组失败: 不是数组类型");
        }
    }

    /**
     * 断言数组包含键
     */
    public static function assertArrayHasKey($key, $array, $message = '')
    {
        if (array_key_exists($key, $array)) {
            self::$passCount++;
            self::logResult(true, $message ?: "断言数组包含键 '$key' 通过");
        } else {
            self::$failCount++;
            self::logResult(false, $message ?: "断言数组包含键 '$key' 失败");
        }
    }

    /**
     * 断言数组不包含键
     */
    public static function assertArrayNotHasKey($key, $array, $message = '')
    {
        if (!array_key_exists($key, $array)) {
            self::$passCount++;
            self::logResult(true, $message ?: "断言数组不包含键 '$key' 通过");
        } else {
            self::$failCount++;
            self::logResult(false, $message ?: "断言数组不包含键 '$key' 失败");
        }
    }

    /**
     * 断言为false
     */
    public static function assertFalse($value, $message = '')
    {
        if ($value === false) {
            self::$passCount++;
            self::logResult(true, $message ?: "断言为false通过");
        } else {
            self::$failCount++;
            self::logResult(false, $message ?: "断言为false失败");
        }
    }

    /**
     * 断言为true
     */
    public static function assertTrue($value, $message = '')
    {
        if ($value === true) {
            self::$passCount++;
            self::logResult(true, $message ?: "断言为true通过");
        } else {
            self::$failCount++;
            self::logResult(false, $message ?: "断言为true失败");
        }
    }

    /**
     * 断言为空
     */
    public static function assertEmpty($value, $message = '')
    {
        if (empty($value)) {
            self::$passCount++;
            self::logResult(true, $message ?: "断言为空通过");
        } else {
            self::$failCount++;
            self::logResult(false, $message ?: "断言为空失败");
        }
    }

    /**
     * 断言数组长度
     */
    public static function assertCount($expectedCount, $array, $message = '')
    {
        $actualCount = count($array);
        if ($expectedCount === $actualCount) {
            self::$passCount++;
            self::logResult(true, $message ?: "断言数组长度通过: $expectedCount");
        } else {
            self::$failCount++;
            self::logResult(false, $message ?: "断言数组长度失败: 期望 $expectedCount, 实际 $actualCount");
        }
    }

    /**
     * 测试异常
     */
    public static function expectException($callable, $expectedMessage = '', $message = '')
    {
        try {
            $callable();
            self::$failCount++;
            self::logResult(false, $message ?: "期望抛出异常但没有抛出");
        } catch (\Exception $e) {
            if (empty($expectedMessage) || strpos($e->getMessage(), $expectedMessage) !== false) {
                self::$passCount++;
                self::logResult(true, $message ?: "异常测试通过: " . $e->getMessage());
            } else {
                self::$failCount++;
                self::logResult(false, $message ?: "异常消息不匹配: 期望包含 '$expectedMessage', 实际 '" . $e->getMessage() . "'");
            }
        }
    }

    /**
 * 记录测试结果
     */
    public static function logResult($passed, $message)
    {
        $status = $passed ? '✅ PASS' : '❌ FAIL';
        $result = "[{$status}] {$message}";
        self::$testResults[] = $result;
        echo $result . "\n";
    }

    /**
     * 获取测试统计
     */
    public static function getStats()
    {
        return [
            'pass' => self::$passCount,
            'fail' => self::$failCount,
            'total' => self::$passCount + self::$failCount,
            'results' => self::$testResults
        ];
    }

    /**
     * 重置测试计数
     */
    public static function reset()
    {
        self::$passCount = 0;
        self::$failCount = 0;
        self::$testResults = [];
    }
}

/**
 * FBA库存汇总统计测试类
 */
class FbaStorageSummaryTest
{
    private $model;
    private $controller;
    private $form;
    private $testSyncDate;

    public function __construct()
    {
        $this->model = new fbaStorageSummaryModel();
        $this->controller = new fbaStorageSummaryController();
        $this->form = new fbaStorageSummaryForm();
        $this->testSyncDate = date('Y-m-d');
    }

    /**
     * 测试模型基础功能
     */
    public function testModelBasicFunctions()
    {
        echo "\n🧪 测试模型基础功能\n";
        echo "------------------------------------\n";

        // 测试获取层级类型映射
        $levelTypeMap = $this->model->getLevelTypeMap();
        TestHelper::assertIsArray($levelTypeMap, "层级类型映射应为数组");
        TestHelper::assertArrayHasKey(1, $levelTypeMap, "层级类型映射应包含键1");
        TestHelper::assertEquals('店铺级', $levelTypeMap[1], "层级1应为店铺级");

        // 测试获取产品阶段映射
        $productStageMap = $this->model->getProductStageMap();
        TestHelper::assertIsArray($productStageMap, "产品阶段映射应为数组");
        TestHelper::assertArrayHasKey(1, $productStageMap, "产品阶段映射应包含键1");
        TestHelper::assertEquals('成长期', $productStageMap[1], "产品阶段1应为成长期");

        // 测试获取备货定位映射
        $stockPositioningMap = $this->model->getStockPositioningMap();
        TestHelper::assertIsArray($stockPositioningMap, "备货定位映射应为数组");
        TestHelper::assertArrayHasKey(1, $stockPositioningMap, "备货定位映射应包含键1");
        TestHelper::assertEquals('重点备货', $stockPositioningMap[1], "备货定位1应为重点备货");

        // 测试获取产品定位映射
        $productPositioningMap = $this->model->getProductPositioningMap();
        TestHelper::assertIsArray($productPositioningMap, "产品定位映射应为数组");
        TestHelper::assertArrayHasKey(1, $productPositioningMap, "产品定位映射应包含键1");
        TestHelper::assertEquals('头部产品', $productPositioningMap[1], "产品定位1应为头部产品");
    }

    /**
     * 测试获取汇总列表
     */
    public function testGetSummaryList()
    {
        echo "\n🧪 测试获取汇总列表\n";
        echo "------------------------------------\n";

        $params = [
            'page' => 1,
            'page_size' => 10,
            'level_type' => 1
        ];

        $result = $this->model->getSummaryList($params);
        
        TestHelper::assertIsArray($result, "返回结果应为数组");
        TestHelper::assertArrayHasKey('total', $result, "结果应包含total字段");
        TestHelper::assertArrayHasKey('page', $result, "结果应包含page字段");
        TestHelper::assertArrayHasKey('list', $result, "结果应包含list字段");
        TestHelper::assertIsArray($result['list'], "list字段应为数组");
    }

    /**
     * 测试表单验证 - 列表参数
     */
    public function testValidateListParams()
    {
        echo "\n🧪 测试表单验证 - 列表参数\n";
        echo "------------------------------------\n";

        // 测试正确参数
        $validParams = [
            'page' => 1,
            'page_size' => 20,
            'level_type' => 1,
            'asin' => 'B07ABC123',
            'sync_date' => '2025-06-20'
        ];
        
        $result = $this->form->validateListParams($validParams);
        TestHelper::assertIsArray($result, "验证结果应为数组");
        TestHelper::assertEquals(1, $result['page'], "页码应为1");
        TestHelper::assertEquals(20, $result['page_size'], "页面大小应为20");

        // 测试无效页码
        TestHelper::expectException(function() {
            $this->form->validateListParams(['page' => 0]);
        }, '页码必须大于0', "无效页码应抛出异常");
    }

    /**
     * 测试表单验证 - 编辑参数
     */
    public function testValidateEditParams()
    {
        echo "\n🧪 测试表单验证 - 编辑参数\n";
        echo "------------------------------------\n";

        // 测试正确参数
        $validParams = [
            'planned_purchase_qty' => 100,
            'purchase_days' => 30,
            'purchase_pending_qty' => 50
        ];
        
        $result = $this->form->validateEditParams($validParams);
        TestHelper::assertIsArray($result, "验证结果应为数组");
        TestHelper::assertEquals(100, $result['planned_purchase_qty'], "计划采购数量应为100");
        TestHelper::assertEquals(30, $result['purchase_days'], "采购天数应为30");
        TestHelper::assertEquals(50, $result['purchase_pending_qty'], "采购未交数量应为50");

        // 测试负数
        TestHelper::expectException(function() {
            $this->form->validateEditParams(['planned_purchase_qty' => -10]);
        }, '计划采购数量不能小于0', "负数应抛出异常");
    }

    /**
     * 测试数据清理和转换
     */
    public function testSanitizeData()
    {
        echo "\n🧪 测试数据清理和转换\n";
        echo "------------------------------------\n";

        $data = [
            'planned_purchase_qty' => '100',
            'purchase_days' => '30',
            'asin' => '  B07ABC123  ',
            'sku' => '  TEST-SKU-001  '
        ];

        $result = $this->form->sanitizeData($data);
        
        TestHelper::assertEquals(100, $result['planned_purchase_qty'], "字符串应转换为整数");
        TestHelper::assertEquals(30, $result['purchase_days'], "字符串应转换为整数");
        TestHelper::assertEquals('B07ABC123', $result['asin'], "字符串应去除空格");
        TestHelper::assertEquals('TEST-SKU-001', $result['sku'], "字符串应去除空格");
    }

    /**
     * 测试批量编辑参数验证
     */
    public function testValidateBatchEditParams()
    {
        echo "\n🧪 测试批量编辑参数验证\n";
        echo "------------------------------------\n";

        $validItems = [
            [
                'id' => 1,
                'planned_purchase_qty' => 100,
                'purchase_days' => 30
            ],
            [
                'id' => 2,
                'planned_purchase_qty' => 200,
                'purchase_pending_qty' => 50
            ]
        ];

        $result = $this->form->validateBatchEditParams($validItems);
        TestHelper::assertIsArray($result, "验证结果应为数组");
        TestHelper::assertCount(2, $result, "结果数量应为2");
        TestHelper::assertEquals(1, $result[0]['id'], "第一项ID应为1");
        TestHelper::assertEquals(100, $result[0]['planned_purchase_qty'], "第一项计划采购数量应为100");

        // 测试无效ID
        TestHelper::expectException(function() {
            $this->form->validateBatchEditParams([['id' => 'invalid']]);
        }, '第0项ID无效', "无效ID应抛出异常");
    }

    /**
     * 测试日期格式验证
     */
    public function testDateFormatValidation()
    {
        echo "\n🧪 测试日期格式验证\n";
        echo "------------------------------------\n";

        // 正确日期格式
        $validParams = ['sync_date' => '2025-06-20'];
        $result = $this->form->validateListParams($validParams);
        TestHelper::assertEquals('2025-06-20', $result['sync_date'], "正确日期格式应通过验证");

        // 错误日期格式
        TestHelper::expectException(function() {
            $this->form->validateListParams(['sync_date' => '20250620']);
        }, '同步日期格式错误', "错误日期格式应抛出异常");
    }

    /**
     * 测试控制器字典数据接口
     */
    public function testGetDictionaries()
    {
        echo "\n🧪 测试控制器字典数据接口\n";
        echo "------------------------------------\n";

        // 模拟获取字典数据
        ob_start();
        $response = $this->controller->getDictionaries();
        ob_end_clean();

        TestHelper::assertIsArray($response, "响应应为数组");
        TestHelper::assertArrayHasKey('code', $response, "响应应包含code字段");
        TestHelper::assertEquals(200, $response['code'], "响应码应为200");
        TestHelper::assertArrayHasKey('data', $response, "响应应包含data字段");
        
        $data = $response['data'];
        TestHelper::assertArrayHasKey('level_type', $data, "数据应包含level_type字段");
        TestHelper::assertArrayHasKey('product_stage', $data, "数据应包含product_stage字段");
        TestHelper::assertArrayHasKey('stock_positioning', $data, "数据应包含stock_positioning字段");
        TestHelper::assertArrayHasKey('product_positioning', $data, "数据应包含product_positioning字段");
    }

    /**
     * 测试层级值验证
     */
    public function testLevelTypeValidation()
    {
        echo "\n🧪 测试层级值验证\n";
        echo "------------------------------------\n";

        // 测试有效层级值
        $validLevels = [1, 2, 3, 4];
        foreach ($validLevels as $level) {
            $params = ['level_type' => $level];
            $result = $this->form->validateListParams($params);
            TestHelper::assertEquals($level, $result['level_type'], "层级值 $level 应通过验证");
        }

        // 测试无效层级值
        TestHelper::expectException(function() {
            $this->form->validateListParams(['level_type' => 5]);
        }, '汇总层级值无效', "无效层级值应抛出异常");
    }

    /**
     * 测试边界值
     */
    public function testBoundaryValues()
    {
        echo "\n🧪 测试边界值\n";
        echo "------------------------------------\n";

        // 测试页码边界值
        $result = $this->form->validateListParams(['page' => 1]);
        TestHelper::assertEquals(1, $result['page'], "最小页码应通过验证");

        // 测试页面大小边界值
        $result = $this->form->validateListParams(['page_size' => 100]);
        TestHelper::assertEquals(100, $result['page_size'], "最大页面大小应通过验证");

        // 测试超出边界值
        TestHelper::expectException(function() {
            $this->form->validateListParams(['page_size' => 101]);
        }, '每页数量不能超过100', "超出边界值应抛出异常");
    }

    /**
     * 测试字符串长度验证
     */
    public function testStringLengthValidation()
    {
        echo "\n🧪 测试字符串长度验证\n";
        echo "------------------------------------\n";

        // 测试ASIN长度
        $longAsin = str_repeat('A', 51);
        TestHelper::expectException(function() use ($longAsin) {
            $this->form->validateListParams(['asin' => $longAsin]);
        }, 'ASIN长度不能超过50', "超长ASIN应抛出异常");
    }

    /**
     * 测试空值处理
     */
    public function testEmptyValueHandling()
    {
        echo "\n🧪 测试空值处理\n";
        echo "------------------------------------\n";

        // 测试空参数
        $result = $this->form->validateListParams([]);
        TestHelper::assertIsArray($result, "空参数应返回数组");
        TestHelper::assertEmpty($result, "空参数应返回空数组");

        // 测试部分空值
        $params = [
            'page' => 1,
            'asin' => '',
            'sku' => null
        ];
        $result = $this->form->validateListParams($params);
        TestHelper::assertEquals(1, $result['page'], "非空参数应保留");
        TestHelper::assertArrayNotHasKey('asin', $result, "空字符串参数应被过滤");
        TestHelper::assertArrayNotHasKey('sku', $result, "null参数应被过滤");
    }

    /**
     * 测试模型异常处理
     */
    public function testModelExceptionHandling()
    {
        echo "\n🧪 测试模型异常处理\n";
        echo "------------------------------------\n";

        // 测试无效ID查询
        $detail = $this->model->getSummaryDetail(999999);
        TestHelper::assertFalse($detail, "无效ID查询应返回false");

        // 测试无效数据更新
        $affectedRows = $this->model->updateEditableFields(999999, [
            'planned_purchase_qty' => 100
        ]);
        TestHelper::assertEquals(0, $affectedRows, "无效ID更新应返回0");
    }

    /**
     * 测试批量汇总功能 - 数量计算
     */
    public function testCalculateQuantities()
    {
        echo "\n🧪 测试批量汇总功能 - 数量计算\n";
        echo "------------------------------------\n";

        $testRow = [
            'afn_fulfillable_quantity' => 100,
            'reserved_fc_transfers' => 20,
            'reserved_fc_processing' => 15,
            'afn_inbound_shipped_quantity' => 30,
            'afn_inbound_receiving_quantity' => 25
        ];

        $reflection = new ReflectionClass($this->model);
        $method = $reflection->getMethod('calculateQuantities');
        $method->setAccessible(true);

        $result = $method->invoke($this->model, $testRow);

        TestHelper::assertEquals(100, $result['fba_sellable_qty'], "FBA可售数量计算正确");
        TestHelper::assertEquals(20, $result['fba_pending_transfer_qty'], "FBA待调仓数量计算正确");
        TestHelper::assertEquals(15, $result['fba_transferring_qty'], "FBA调仓中数量计算正确");
        TestHelper::assertEquals(55, $result['fba_inbound_qty'], "FBA在途数量计算正确 (30+25)");
        TestHelper::assertEquals(155, $result['inventory_plus_inbound_qty'], "库存+在途数量计算正确 (100+30+25)");
    }

    /**
     * 测试批量汇总功能 - 价格计算
     */
    public function testCalculatePrices()
    {
        echo "\n🧪 测试批量汇总功能 - 价格计算\n";
        echo "------------------------------------\n";

        $testRow = [
            'afn_fulfillable_quantity_price' => 1000.50,
            'reserved_fc_transfers_price' => 200.25,
            'reserved_fc_processing_price' => 150.75,
            'afn_inbound_shipped_quantity_price' => 300.00,
            'afn_inbound_receiving_quantity_price' => 250.50
        ];

        $reflection = new ReflectionClass($this->model);
        $method = $reflection->getMethod('calculatePrices');
        $method->setAccessible(true);

        $result = $method->invoke($this->model, $testRow);

        TestHelper::assertEquals(1000.50, $result['fba_sellable_price'], "FBA可售价格计算正确");
        TestHelper::assertEquals(200.25, $result['fba_pending_transfer_price'], "FBA待调仓价格计算正确");
        TestHelper::assertEquals(150.75, $result['fba_transferring_price'], "FBA调仓中价格计算正确");
        TestHelper::assertEquals(550.50, $result['fba_inbound_price'], "FBA在途价格计算正确 (300.00+250.50)");
        TestHelper::assertEquals(1551.00, $result['inventory_plus_inbound_price'], "库存+在途价格计算正确 (1000.50+300.00+250.50)");
    }

    /**
     * 测试批量汇总功能 - 维度数据聚合
     */
    public function testAggregateDataNew()
    {
        echo "\n🧪 测试批量汇总功能 - 维度数据聚合\n";
        echo "------------------------------------\n";

        $dimensionData = [
            'fba_sellable_qty' => 50,
            'fba_sellable_price' => 500.00,
            'shop_list' => [123],
            'site_list' => ['US'],
            'sku_list' => ['SKU001'],
            'shop_count' => 1,
            'site_count' => 1,
            'sku_count' => 1
        ];

        $quantities = ['fba_sellable_qty' => 30];
        $prices = ['fba_sellable_price' => 300.00];
        $row = ['sid' => 124, 'site_code' => 'EU', 'sku' => 'SKU002'];

        $reflection = new ReflectionClass($this->model);
        $method = $reflection->getMethod('aggregateDataNew');
        $method->setAccessible(true);

        $method->invokeArgs($this->model, [&$dimensionData, $quantities, $prices, $row]);

        TestHelper::assertEquals(80, $dimensionData['fba_sellable_qty'], "数量字段累加正确 (50+30)");
        TestHelper::assertEquals(800.00, $dimensionData['fba_sellable_price'], "价格字段累加正确 (500.00+300.00)");
        TestHelper::assertEquals(2, $dimensionData['shop_count'], "店铺计数更新正确");
        TestHelper::assertEquals(2, $dimensionData['site_count'], "站点计数更新正确");
        TestHelper::assertEquals(2, $dimensionData['sku_count'], "SKU计数更新正确");
    }

    /**
     * 测试批量汇总功能 - 批量数据处理
     */
    public function testProcessBatchSummary()
    {
        echo "\n🧪 测试批量汇总功能 - 批量数据处理\n";
        echo "------------------------------------\n";

        $batchData = [
            [
                'asin' => 'B07TEST123',
                'sku' => 'TEST-SKU-001',
                'site_code' => 'US',
                'sid' => 123,
                'shop_name' => '测试店铺1',
                'product_stage' => 2,
                'stock_positioning' => 1,
                'product_positioning' => 1,
                'afn_fulfillable_quantity' => 100,
                'afn_fulfillable_quantity_price' => 1000.00,
                'reserved_fc_transfers' => 0,
                'reserved_fc_processing' => 0,
                'afn_inbound_shipped_quantity' => 0,
                'afn_inbound_receiving_quantity' => 0,
                'reserved_fc_transfers_price' => 0,
                'reserved_fc_processing_price' => 0,
                'afn_inbound_shipped_quantity_price' => 0,
                'afn_inbound_receiving_quantity_price' => 0
            ],
            [
                'asin' => 'B07TEST123',
                'sku' => 'TEST-SKU-002',
                'site_code' => 'US',
                'sid' => 124,
                'shop_name' => '测试店铺2',
                'product_stage' => 2,
                'stock_positioning' => 1,
                'product_positioning' => 2,
                'afn_fulfillable_quantity' => 50,
                'afn_fulfillable_quantity_price' => 500.00,
                'reserved_fc_transfers' => 10,
                'reserved_fc_processing' => 5,
                'afn_inbound_shipped_quantity' => 20,
                'afn_inbound_receiving_quantity' => 15,
                'reserved_fc_transfers_price' => 100.00,
                'reserved_fc_processing_price' => 50.00,
                'afn_inbound_shipped_quantity_price' => 200.00,
                'afn_inbound_receiving_quantity_price' => 150.00
            ]
        ];

        $reflection = new ReflectionClass($this->model);
        $method = $reflection->getMethod('processBatchSummary');
        $method->setAccessible(true);

        $result = $method->invoke($this->model, $batchData, $this->testSyncDate);

        TestHelper::assertIsArray($result, "批量处理结果应为数组");
        TestHelper::assertTrue(count($result) >= 4, "应生成至少4个维度的数据");

        // 检查ASIN级汇总（应该合并两条记录）
        $asinLevel = null;
        foreach ($result as $item) {
            if ($item['level_type'] == 4 && $item['asin'] == 'B07TEST123') {
                $asinLevel = $item;
                break;
            }
        }

        TestHelper::assertTrue($asinLevel !== null, "ASIN级汇总数据存在");
        TestHelper::assertEquals(150, $asinLevel['fba_sellable_qty'], "ASIN级数量汇总正确 (100+50)");
        TestHelper::assertEquals(1500.00, $asinLevel['fba_sellable_price'], "ASIN级价格汇总正确 (1000.00+500.00)");
    }

    /**
     * 测试批量汇总功能 - 完整流程（模拟测试）
     */
    public function testBatchSummarizeFromDetail()
    {
        echo "\n🧪 测试批量汇总功能 - 完整流程（模拟测试）\n";
        echo "------------------------------------\n";

        try {
            // 使用小批量大小进行测试
            $result = $this->model->batchSummarizeFromDetail($this->testSyncDate, 10);

            // 检查返回结果结构
            $requiredKeys = ['success', 'total_processed', 'total_inserted', 'total_updated', 'start_time', 'end_time'];
            foreach ($requiredKeys as $key) {
                TestHelper::assertArrayHasKey($key, $result, "结果应包含 $key 字段");
            }

            TestHelper::assertTrue(is_bool($result['success']), "success字段应为布尔值");
            TestHelper::assertTrue(is_numeric($result['total_processed']), "total_processed字段应为数字");
            TestHelper::assertTrue(is_numeric($result['total_inserted']), "total_inserted字段应为数字");
            TestHelper::assertTrue(is_numeric($result['total_updated']), "total_updated字段应为数字");

        } catch (Exception $e) {
            echo "⚠ 批量汇总测试跳过（需要数据库连接）: " . $e->getMessage() . "\n";
            TestHelper::logResult(true, "批量汇总测试跳过（需要数据库连接）");
        }
    }

    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "\n====================================\n";
        echo "🚀 开始运行FBA库存汇总统计测试\n";
        echo "====================================\n";

        TestHelper::reset();

        // 运行所有测试方法
        $testMethods = [
            // 'testModelBasicFunctions',
            // 'testGetSummaryList',
            // 'testValidateListParams',
            // 'testValidateEditParams',
            // 'testSanitizeData',
            // 'testValidateBatchEditParams',
            // 'testDateFormatValidation',
            // // 'testGetDictionaries',
            // 'testLevelTypeValidation',
            // 'testBoundaryValues',
            // 'testStringLengthValidation',
            // 'testEmptyValueHandling',
            // 'testModelExceptionHandling',
            // 'testCalculateQuantities',
            // 'testCalculatePrices',
            // 'testAggregateDataNew',
            // 'testProcessBatchSummary',
            'testBatchSummarizeFromDetail'
        ];

        foreach ($testMethods as $method) {
            try {
                $this->$method();
            } catch (\Exception $e) {
                echo "❌ 测试方法 $method 执行失败: " . $e->getMessage() . "\n";
                // 记录测试方法执行失败
                TestHelper::logResult(false, "测试方法 $method 执行失败: " . $e->getMessage());
            }
        }

        $stats = TestHelper::getStats();
        
        echo "\n====================================\n";
        echo "📊 测试统计结果\n";
        echo "====================================\n";
        echo "总测试数: " . $stats['total'] . "\n";
        echo "通过数: " . $stats['pass'] . " ✅\n";
        echo "失败数: " . $stats['fail'] . " ❌\n";
        echo "成功率: " . ($stats['total'] > 0 ? round($stats['pass'] / $stats['total'] * 100, 2) : 0) . "%\n";
        echo "====================================\n";

        return $stats['fail'] === 0;
    }
}

/**
 * 运行测试的辅助函数
 */
function runFbaStorageSummaryTests()
{
    $tester = new FbaStorageSummaryTest();
    return $tester->runAllTests();
}

// 如果直接运行此文件，执行测试
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    $success = runFbaStorageSummaryTests();
    exit($success ? 0 : 1);
}
