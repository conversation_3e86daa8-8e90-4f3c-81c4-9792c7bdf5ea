<?php
/**
 * 海外仓备货单控制器
 * @purpose 海外仓备货单管理
 * @Author: System
 * @Time: 2025/06/24
 */

namespace plugins\logistics\controller;

use plugins\logistics\models\overseasInboundModel;
use plugins\logistics\form\overseasInboundForm;
use core\lib\log;

class overseasInboundController extends baseController
{
    private $model;
    
    public function __construct()
    {
        $this->model = new overseasInboundModel();
        parent::__construct();
    }

    /**
     * 获取备货单列表
     */
    public function getInboundList()
    {
        try {
            $form = new overseasInboundForm();
            $params = $form->validateListParams($_GET);
            
            if (!empty($form->getErrors())) {
                returnError(implode(', ', $form->getErrors()));
            }
            
            $result = $this->model->getInboundList($params);
            
            // 格式化返回数据
            if (!empty($result['list'])) {
                foreach ($result['list'] as &$item) {
                    // 解析JSON字段
                    $item['products'] = json_decode($item['products'] ?? '[]', true);
                    $item['logistics'] = json_decode($item['logistics'] ?? '[]', true);
                    $item['head_logistics_list'] = json_decode($item['head_logistics_list'] ?? '{}', true);
                    
                    // 添加状态名称
                    $statusMap = $this->model->getStatusMap();
                    $item['status_name'] = $statusMap[$item['status']] ?? '未知';
                    
                    $subStatusMap = $this->model->getSubStatusMap();
                    $item['sub_status_name'] = $subStatusMap[$item['sub_status']] ?? '';
                }
            }
            
            returnSuccess($result);
        } catch (\Exception $e) {
            log::lingXingApi('OverseasInbound')->error('获取备货单列表失败：' . $e->getMessage());
            returnError('获取备货单列表失败');
        }
    }

    /**
     * 更新可编辑字段
     */
    public function edit()
    {
        try {
            $form = new overseasInboundForm();
            $data = $form->validateEditableFields($_POST);
            
            if (!empty($form->getErrors())) {
                returnError(implode(', ', $form->getErrors()));
            }
            
            $id = $_POST['id'] ?? 0;
            if (empty($id)) {
                returnError('备货单ID不能为空');
            }
            
            $result = $this->model->updateEditableFields($id, $data);
            if ($result) {
                log::lingXingApi('OverseasInbound')->info("更新备货单可编辑字段成功，ID：{$id}");
                returnSuccess(['updated' => $result], '更新成功');
            } else {
                returnError('更新失败');
            }
        } catch (\Exception $e) {
            log::lingXingApi('OverseasInbound')->error('更新可编辑字段失败：' . $e->getMessage());
            returnError('更新失败');
        }
    }

    /**
     * 导入备货单数据
     */
    public function import()
    {
        try {
            $form = new overseasInboundForm();
            $data = $form->validateImportParams($_POST);
            
            if (!empty($form->getErrors())) {
                returnError(implode(', ', $form->getErrors()));
            }
            
            $excelUrl = SELF_FK . $data['excel_src'];
            if (!file_exists($excelUrl)) {
                returnError('表格文件不存在');
            }
            
            $excelData = (new FastExcel())->import($excelUrl, function ($row) {
                // 将每一项 trim 去空格，再判断整行是否为空
                $cleaned = collect($row)->map(function ($value) {
                    if (is_string($value)) {
                        return trim((string)$value);
                    }
                    return $value;
                });
                // 全部为空则跳过
                if ($cleaned->filter()->isEmpty()) {
                    return null;
                }
                return $cleaned->toArray(); // 返回处理后的数据
            });
            
            $importData = [];
            foreach ($excelData as $row) {
                $importData[] = [
                    'overseas_order_no' => $row['备货单号'] ?? '',
                    'warehouse_code' => $row['入库编码'] ?? '',
                    's_wname' => $row['发货仓'] ?? '',
                    'r_wname' => $row['收货仓'] ?? '',
                    'transparent_label' => $row['透明标'] ?? '',
                    'shop_code' => $row['店铺代码'] ?? '',
                    'fnsku' => $row['FNSKU'] ?? '',
                    'remaining_available' => $row['剩余可用'] ?? 0,
                    'shipping_remark' => $row['发货备注'] ?? '',
                    'other_remark' => $row['其他备注'] ?? ''
                ];
            }
            
            $result = $this->model->importData($importData);
            
            if ($result) {
                log::lingXingApi('OverseasInbound')->info('导入备货单数据成功，共' . count($importData) . '条');
                returnSuccess(['imported' => count($importData)], '导入成功');
            } else {
                returnError('导入失败');
            }
        } catch (\Exception $e) {
            log::lingXingApi('OverseasInbound')->error('导入备货单数据失败：' . $e->getMessage());
            returnError('导入失败');
        }
    }


    /**
     * 导出备货单数据
     */
    public function export()
    {
        try {
            $form = new overseasInboundForm();
            $params = $form->validateListParams($_POST);
            
            if (!empty($form->getErrors())) {
                returnError(implode(', ', $form->getErrors()));
            }
            
            // 设置最大导出数量
            $params['page_size'] = 1000;
            $params['page'] = 1;
            
            $result = $this->model->getInboundList($params);
            
            if (empty($result['list'])) {
                returnError('没有数据可导出');
            }
            
            // 准备导出数据
            $exportData = [];
            $headers = [
                '备货单号', '入库编码', '发货仓', '收货仓', '状态', '创建时间',
                '透明标', '店铺代码', 'FNSKU', '剩余可用', '发货备注', '其他备注'
            ];
            
            $exportData[] = $headers;
            
            $statusMap = $this->model->getStatusMap();
            
            foreach ($result['list'] as $item) {
                $exportData[] = [
                    $item['overseas_order_no'],
                    $item['warehouse_code'],
                    $item['s_wname'],
                    $item['r_wname'],
                    $statusMap[$item['status']] ?? '未知',
                    $item['create_time'],
                    $item['transparent_label'],
                    $item['shop_code'],
                    $item['fnsku'],
                    $item['remaining_available'],
                    $item['shipping_remark'],
                    $item['other_remark']
                ];
            }
            
            log::lingXingApi('OverseasInbound')->info('导出备货单数据，共' . count($result['list']) . '条');
            returnSuccess(['data' => $exportData, 'filename' => '海外仓备货单_' . date('Y-m-d_H-i-s') . '.csv']);
        } catch (\Exception $e) {
            log::lingXingApi('OverseasInbound')->error('导出备货单数据失败：' . $e->getMessage());
            returnError('导出失败');
        }
    }
}
