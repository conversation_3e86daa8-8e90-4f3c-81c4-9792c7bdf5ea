<?php
/**
 * 海外仓备货单控制器
 * @purpose 海外仓备货单管理
 * @Author: System
 * @Time: 2025/06/24
 */

namespace plugins\logistics\controller;

use plugins\logistics\models\overseasInboundModel;
use plugins\logistics\form\overseasInboundForm;
use core\lib\log;

class overseasInboundController
{
    private $model;
    
    public function __construct()
    {
        $this->model = new overseasInboundModel();
    }

    /**
     * 获取备货单列表
     */
    public function getInboundList()
    {
        try {
            $form = new overseasInboundForm();
            $params = $form->validateListParams($_GET);
            
            if (!empty($form->getErrors())) {
                returnError(implode(', ', $form->getErrors()));
            }
            
            $result = $this->model->getInboundList($params);
            
            // 格式化返回数据
            if (!empty($result['list'])) {
                foreach ($result['list'] as &$item) {
                    // 解析JSON字段
                    $item['products'] = json_decode($item['products'] ?? '[]', true);
                    $item['logistics'] = json_decode($item['logistics'] ?? '[]', true);
                    $item['head_logistics_list'] = json_decode($item['head_logistics_list'] ?? '{}', true);
                    
                    // 添加状态名称
                    $statusMap = $this->model->getStatusMap();
                    $item['status_name'] = $statusMap[$item['status']] ?? '未知';
                    
                    $subStatusMap = $this->model->getSubStatusMap();
                    $item['sub_status_name'] = $subStatusMap[$item['sub_status']] ?? '';
                }
            }
            
            returnSuccess($result);
        } catch (\Exception $e) {
            log::lingXingApi('OverseasInbound')->error('获取备货单列表失败：' . $e->getMessage());
            returnError('获取备货单列表失败');
        }
    }

    /**
     * 获取备货单详情
     */
    public function getInboundDetail()
    {
        try {
            $id = $_POST['id'] ?? 0;
            if (empty($id)) {
                returnError('备货单ID不能为空');
            }
            
            $data = $this->model->getInboundDetail($id);
            if (!$data) {
                returnError('备货单不存在');
            }
            
            // 添加状态名称
            $statusMap = $this->model->getStatusMap();
            $data['status_name'] = $statusMap[$data['status']] ?? '未知';
            
            $subStatusMap = $this->model->getSubStatusMap();
            $data['sub_status_name'] = $subStatusMap[$data['sub_status']] ?? '';
            
            returnSuccess($data);
        } catch (\Exception $e) {
            log::lingXingApi('OverseasInbound')->error('获取备货单详情失败：' . $e->getMessage());
            returnError('获取备货单详情失败');
        }
    }

    /**
     * 更新可编辑字段
     */
    public function updateEditableFields()
    {
        try {
            $form = new overseasInboundForm();
            $data = $form->validateEditableFields($_POST);
            
            if (!empty($form->getErrors())) {
                returnError(implode(', ', $form->getErrors()));
            }
            
            $id = $_POST['id'] ?? 0;
            if (empty($id)) {
                returnError('备货单ID不能为空');
            }
            
            $result = $this->model->updateEditableFields($id, $data);
            if ($result) {
                log::lingXingApi('OverseasInbound')->info("更新备货单可编辑字段成功，ID：{$id}");
                returnSuccess(['updated' => $result], '更新成功');
            } else {
                returnError('更新失败');
            }
        } catch (\Exception $e) {
            log::lingXingApi('OverseasInbound')->error('更新可编辑字段失败：' . $e->getMessage());
            returnError('更新失败');
        }
    }

    /**
     * 删除备货单
     */
    public function deleteInbound()
    {
        try {
            $id = $_POST['id'] ?? 0;
            if (empty($id)) {
                returnError('备货单ID不能为空');
            }
            
            $result = $this->model->deleteInbound($id);
            if ($result) {
                log::lingXingApi('OverseasInbound')->info("删除备货单成功，ID：{$id}");
                returnSuccess(['deleted' => $result], '删除成功');
            } else {
                returnError('删除失败');
            }
        } catch (\Exception $e) {
            log::lingXingApi('OverseasInbound')->error('删除备货单失败：' . $e->getMessage());
            returnError('删除失败');
        }
    }

    /**
     * 获取状态选项
     */
    public function getStatusOptions()
    {
        try {
            $data = [
                'status_map' => $this->model->getStatusMap(),
                'sub_status_map' => $this->model->getSubStatusMap(),
                'date_type_map' => $this->model->getDateTypeMap()
            ];
            
            returnSuccess($data);
        } catch (\Exception $e) {
            log::lingXingApi('OverseasInbound')->error('获取状态选项失败：' . $e->getMessage());
            returnError('获取状态选项失败');
        }
    }

    /**
     * 批量更新备货单
     */
    public function batchUpdate()
    {
        try {
            $ids = $_POST['ids'] ?? [];
            $data = $_POST['data'] ?? [];
            
            if (empty($ids) || !is_array($ids)) {
                returnError('请选择要更新的备货单');
            }
            
            $form = new overseasInboundForm();
            $updateData = $form->validateEditableFields($data);
            
            if (!empty($form->getErrors())) {
                returnError(implode(', ', $form->getErrors()));
            }
            
            $successCount = 0;
            foreach ($ids as $id) {
                $result = $this->model->updateEditableFields($id, $updateData);
                if ($result) {
                    $successCount++;
                }
            }
            
            log::lingXingApi('OverseasInbound')->info("批量更新备货单成功，共更新{$successCount}条");
            returnSuccess(['updated' => $successCount], "成功更新{$successCount}条记录");
        } catch (\Exception $e) {
            log::lingXingApi('OverseasInbound')->error('批量更新备货单失败：' . $e->getMessage());
            returnError('批量更新失败');
        }
    }

    /**
     * 导出备货单数据
     */
    public function exportInbound()
    {
        try {
            $form = new overseasInboundForm();
            $params = $form->validateListParams($_POST);
            
            if (!empty($form->getErrors())) {
                returnError(implode(', ', $form->getErrors()));
            }
            
            // 设置最大导出数量
            $params['page_size'] = 1000;
            $params['page'] = 1;
            
            $result = $this->model->getInboundList($params);
            
            if (empty($result['list'])) {
                returnError('没有数据可导出');
            }
            
            // 准备导出数据
            $exportData = [];
            $headers = [
                '备货单号', '入库编码', '发货仓', '收货仓', '状态', '创建时间',
                '透明标', '店铺代码', 'FNSKU', '剩余可用', '发货备注', '其他备注'
            ];
            
            $exportData[] = $headers;
            
            $statusMap = $this->model->getStatusMap();
            
            foreach ($result['list'] as $item) {
                $exportData[] = [
                    $item['overseas_order_no'],
                    $item['warehouse_code'],
                    $item['s_wname'],
                    $item['r_wname'],
                    $statusMap[$item['status']] ?? '未知',
                    $item['create_time'],
                    $item['transparent_label'],
                    $item['shop_code'],
                    $item['fnsku'],
                    $item['remaining_available'],
                    $item['shipping_remark'],
                    $item['other_remark']
                ];
            }
            
            log::lingXingApi('OverseasInbound')->info('导出备货单数据，共' . count($result['list']) . '条');
            returnSuccess(['data' => $exportData, 'filename' => '海外仓备货单_' . date('Y-m-d_H-i-s') . '.csv']);
        } catch (\Exception $e) {
            log::lingXingApi('OverseasInbound')->error('导出备货单数据失败：' . $e->getMessage());
            returnError('导出失败');
        }
    }
}
