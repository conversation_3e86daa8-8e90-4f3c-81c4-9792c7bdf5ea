<?xml version="1.0" encoding="UTF-8"?>
<task_registry>
    <meta>
        <version>1.0</version>
        <last_updated>2025-07-01</last_updated>
        <description>物流模块任务注册表</description>
    </meta>

    <projects>
        <project id="overseas_inbound_sku_split" status="completed">
            <name>海外仓备货单SKU拆分项目</name>
            <description>从原始备货单数据中按SKU维度提取并重组数据到新的明细表</description>
            <file>overseas_inbound_sku_split.xml</file>
            <completion_date>2025-07-01</completion_date>
            <priority>high</priority>
        </project>
    </projects>

    <active_projects>
        <!-- 当前活跃项目列表，新项目将添加到这里 -->
    </active_projects>

    <global_context>
        <workspace_root>/mnt/d/phpstudy_pro/WWW/oa-api</workspace_root>
        <module_path>plugins/logistics</module_path>
        <database_connections>
            <source>dbErpMysql</source>
            <target>dbLMysql</target>
        </database_connections>
        <coding_standards>
            <language>PHP</language>
            <framework>原生PHP</framework>
            <database>PDO</database>
            <testing>原生PHP单元测试</testing>
            <batch_size>100</batch_size>
        </coding_standards>
        <notification_system>
            <command>D:\phpstudy_pro\WWW\notify.ps1</command>
            <template>-title "秋儿已经为您实现了$fun功能" -message "为您节省了$time时间"</template>
            <required>true</required>
            <timing>任务完成后必须执行</timing>
        </notification_system>
    </global_context>

    <common_patterns>
        <pattern name="model_structure">
            <location>plugins/logistics/models/</location>
            <naming>[功能名称]Model.php</naming>
            <database_connection>使用dbErpMysql或dbLMysql</database_connection>
            <methods>增删改查、批量处理、数据验证</methods>
        </pattern>
        <pattern name="controller_structure">
            <location>task/controller/logisticsController.php</location>
            <methods>业务逻辑处理、参数验证、错误处理</methods>
            <response_format>SetReturn格式</response_format>
        </pattern>
        <pattern name="test_structure">
            <location>plugins/logistics/tests/</location>
            <naming>[功能名称]Test.php</naming>
            <coverage>单元测试、集成测试、性能测试</coverage>
        </pattern>
        <pattern name="sql_structure">
            <location>plugins/logistics/sql/</location>
            <naming>[表名].sql</naming>
            <features>建表语句、索引、注释</features>
        </pattern>
    </common_patterns>

    <next_project_template>
        <meta>
            <name>[项目名称]</name>
            <description>[项目描述]</description>
            <status>planning</status>
            <priority>[high/medium/low]</priority>
        </meta>
        <requirements>
            <business_goal>[业务目标]</business_goal>
            <technical_requirements>[技术需求]</technical_requirements>
            <data_requirements>[数据需求]</data_requirements>
        </requirements>
        <implementation_plan>
            <phase name="analysis" status="pending">
                <task name="requirement_analysis" priority="high">
                    <description>需求分析和设计</description>
                </task>
            </phase>
            <phase name="development" status="pending">
                <task name="database_design" priority="high">
                    <description>数据库设计</description>
                </task>
                <task name="model_implementation" priority="high">
                    <description>模型实现</description>
                </task>
                <task name="controller_implementation" priority="medium">
                    <description>控制器实现</description>
                </task>
            </phase>
            <phase name="testing" status="pending">
                <task name="unit_tests" priority="medium">
                    <description>单元测试</description>
                </task>
                <task name="integration_tests" priority="medium">
                    <description>集成测试</description>
                </task>
            </phase>
        </implementation_plan>
    </next_project_template>
</task_registry>
